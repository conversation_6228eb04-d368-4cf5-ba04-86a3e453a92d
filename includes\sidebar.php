<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : '' ?>" href="index.php">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'pos.php' ? 'active' : '' ?>" href="pos.php">
                    <i class="fas fa-cash-register"></i>
                    نقطة البيع
                </a>
            </li>
        </ul>
        
        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>إدارة المنتجات</span>
        </h6>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'products.php' ? 'active' : '' ?>" href="products.php">
                    <i class="fas fa-boxes"></i>
                    المنتجات
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'categories.php' ? 'active' : '' ?>" href="categories.php">
                    <i class="fas fa-tags"></i>
                    الفئات
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'barcode.php' ? 'active' : '' ?>" href="barcode.php">
                    <i class="fas fa-barcode"></i>
                    إنتاج باركود
                </a>
            </li>
        </ul>
        
        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>المبيعات والمشتريات</span>
        </h6>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'sales.php' ? 'active' : '' ?>" href="sales.php">
                    <i class="fas fa-shopping-bag"></i>
                    المبيعات
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'purchases.php' ? 'active' : '' ?>" href="purchases.php">
                    <i class="fas fa-shopping-cart"></i>
                    المشتريات
                </a>
            </li>
        </ul>
        
        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>إدارة الأشخاص</span>
        </h6>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'customers.php' ? 'active' : '' ?>" href="customers.php">
                    <i class="fas fa-users"></i>
                    العملاء
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'suppliers.php' ? 'active' : '' ?>" href="suppliers.php">
                    <i class="fas fa-truck"></i>
                    الموردين
                </a>
            </li>
        </ul>
        
        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>المالية</span>
        </h6>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'cash_register.php' ? 'active' : '' ?>" href="cash_register.php">
                    <i class="fas fa-wallet"></i>
                    الخزنة
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'expenses.php' ? 'active' : '' ?>" href="expenses.php">
                    <i class="fas fa-money-bill-wave"></i>
                    المصروفات
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'reports.php' ? 'active' : '' ?>" href="reports.php">
                    <i class="fas fa-chart-bar"></i>
                    التقارير
                </a>
            </li>
        </ul>
        
        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>الإعدادات</span>
        </h6>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'settings.php' ? 'active' : '' ?>" href="settings.php">
                    <i class="fas fa-cog"></i>
                    إعدادات النظام
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'users.php' ? 'active' : '' ?>" href="users.php">
                    <i class="fas fa-user-cog"></i>
                    المستخدمين
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'backup.php' ? 'active' : '' ?>" href="backup.php">
                    <i class="fas fa-download"></i>
                    النسخ الاحتياطي
                </a>
            </li>
        </ul>
        
        <!-- معلومات سريعة -->
        <div class="mt-4 px-3">
            <div class="card bg-primary text-white">
                <div class="card-body p-3">
                    <h6 class="card-title">معلومات سريعة</h6>
                    <?php
                    try {
                        // رصيد الخزنة
                        $stmt = $pdo->prepare("SELECT balance_after FROM cash_register ORDER BY id DESC LIMIT 1");
                        $stmt->execute();
                        $cash_balance = $stmt->fetchColumn() ?: 0;
                        
                        // مبيعات اليوم
                        $stmt = $pdo->prepare("SELECT COALESCE(SUM(total_amount), 0) FROM sales WHERE DATE(created_at) = CURDATE()");
                        $stmt->execute();
                        $today_sales = $stmt->fetchColumn();
                    ?>
                    <p class="card-text mb-1">
                        <small>رصيد الخزنة:</small><br>
                        <strong><?= number_format($cash_balance, 2) ?> ج.م</strong>
                    </p>
                    <p class="card-text mb-0">
                        <small>مبيعات اليوم:</small><br>
                        <strong><?= number_format($today_sales, 2) ?> ج.م</strong>
                    </p>
                    <?php } catch (PDOException $e) {} ?>
                </div>
            </div>
        </div>
        
        <!-- أزرار سريعة -->
        <div class="mt-3 px-3">
            <div class="d-grid gap-2">
                <a href="pos.php" class="btn btn-success btn-sm">
                    <i class="fas fa-plus"></i> بيع جديد
                </a>
                <a href="products.php?action=add" class="btn btn-info btn-sm">
                    <i class="fas fa-box"></i> منتج جديد
                </a>
            </div>
        </div>
    </div>
</nav>

<style>
.sidebar {
    position: fixed;
    top: 76px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    overflow-y: auto;
}

.sidebar .nav-link {
    color: #333;
    padding: 0.75rem 1rem;
    border-radius: 0;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.sidebar .nav-link.active {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
    border-right: 3px solid #007bff;
}

.sidebar .nav-link i {
    margin-left: 0.5rem;
    width: 16px;
    text-align: center;
}

.sidebar-heading {
    font-size: 0.75rem;
    text-transform: uppercase;
    font-weight: bold;
    letter-spacing: 0.05em;
}

.sidebar .card {
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
}

.sidebar .btn-sm {
    font-size: 0.8rem;
    padding: 0.375rem 0.75rem;
}

/* تحسين للشاشات الصغيرة */
@media (max-width: 767.98px) {
    .sidebar {
        position: relative;
        top: 0;
        height: auto;
    }
    
    .sidebar .position-sticky {
        position: relative !important;
    }
}

/* تحسين التمرير */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.sidebar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>
