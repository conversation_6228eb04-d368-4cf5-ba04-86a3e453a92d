<?php
/**
 * نظام إدارة المحل Z - التقارير
 */

require_once 'config.php';
require_login();

$report_type = $_GET['type'] ?? 'sales';
$date_from = $_GET['date_from'] ?? date('Y-m-01');
$date_to = $_GET['date_to'] ?? date('Y-m-d');

// جلب تقارير المبيعات
if ($report_type === 'sales') {
    try {
        // إجمالي المبيعات
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_invoices,
                COALESCE(SUM(total_amount), 0) as total_sales,
                COALESCE(SUM(paid_amount + wallet_used), 0) as total_paid,
                COALESCE(SUM(remaining_amount), 0) as total_debts
            FROM sales 
            WHERE sale_date BETWEEN ? AND ?
        ");
        $stmt->execute([$date_from, $date_to]);
        $sales_summary = $stmt->fetch();
        
        // المبيعات اليومية
        $stmt = $pdo->prepare("
            SELECT 
                sale_date,
                COUNT(*) as invoices_count,
                SUM(total_amount) as daily_sales
            FROM sales 
            WHERE sale_date BETWEEN ? AND ?
            GROUP BY sale_date
            ORDER BY sale_date DESC
        ");
        $stmt->execute([$date_from, $date_to]);
        $daily_sales = $stmt->fetchAll();
        
        // أفضل المنتجات مبيعاً
        $stmt = $pdo->prepare("
            SELECT 
                p.name,
                SUM(si.quantity) as total_quantity,
                SUM(si.total_price) as total_revenue
            FROM sale_items si
            JOIN products p ON si.product_id = p.id
            JOIN sales s ON si.sale_id = s.id
            WHERE s.sale_date BETWEEN ? AND ?
            GROUP BY p.id, p.name
            ORDER BY total_revenue DESC
            LIMIT 10
        ");
        $stmt->execute([$date_from, $date_to]);
        $top_products = $stmt->fetchAll();
        
        // أفضل العملاء
        $stmt = $pdo->prepare("
            SELECT 
                c.name,
                COUNT(s.id) as purchases_count,
                SUM(s.total_amount) as total_spent
            FROM sales s
            JOIN customers c ON s.customer_id = c.id
            WHERE s.sale_date BETWEEN ? AND ?
            GROUP BY c.id, c.name
            ORDER BY total_spent DESC
            LIMIT 10
        ");
        $stmt->execute([$date_from, $date_to]);
        $top_customers = $stmt->fetchAll();
        
    } catch (PDOException $e) {
        $error = 'خطأ في جلب تقارير المبيعات';
    }
}

// جلب تقارير المخزون
elseif ($report_type === 'inventory') {
    try {
        // إحصائيات المخزون
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_products,
                COUNT(CASE WHEN stock_quantity <= min_stock_level THEN 1 END) as low_stock_products,
                COUNT(CASE WHEN stock_quantity = 0 THEN 1 END) as out_of_stock_products,
                SUM(stock_quantity * cost_price) as inventory_value
            FROM products 
            WHERE is_active = 1
        ");
        $stmt->execute();
        $inventory_summary = $stmt->fetch();
        
        // المنتجات منخفضة المخزون
        $stmt = $pdo->prepare("
            SELECT name, stock_quantity, min_stock_level, cost_price, selling_price
            FROM products 
            WHERE is_active = 1 AND stock_quantity <= min_stock_level
            ORDER BY stock_quantity ASC
        ");
        $stmt->execute();
        $low_stock_products = $stmt->fetchAll();
        
        // حركات المخزون الأخيرة
        $stmt = $pdo->prepare("
            SELECT 
                sm.*, p.name as product_name,
                CASE 
                    WHEN sm.reference_type = 'sale' THEN 'بيع'
                    WHEN sm.reference_type = 'purchase' THEN 'شراء'
                    WHEN sm.reference_type = 'adjustment' THEN 'تعديل'
                    ELSE sm.reference_type
                END as reference_type_ar
            FROM stock_movements sm
            JOIN products p ON sm.product_id = p.id
            WHERE sm.created_at BETWEEN ? AND ?
            ORDER BY sm.created_at DESC
            LIMIT 50
        ");
        $stmt->execute([$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
        $stock_movements = $stmt->fetchAll();
        
    } catch (PDOException $e) {
        $error = 'خطأ في جلب تقارير المخزون';
    }
}

// جلب التقارير المالية
elseif ($report_type === 'financial') {
    try {
        // ملخص مالي
        $stmt = $pdo->prepare("
            SELECT 
                (SELECT COALESCE(SUM(total_amount), 0) FROM sales WHERE sale_date BETWEEN ? AND ?) as total_sales,
                (SELECT COALESCE(SUM(total_amount), 0) FROM purchases WHERE purchase_date BETWEEN ? AND ?) as total_purchases,
                (SELECT COALESCE(SUM(amount), 0) FROM expenses WHERE expense_date BETWEEN ? AND ?) as total_expenses
        ");
        $stmt->execute([$date_from, $date_to, $date_from, $date_to, $date_from, $date_to]);
        $financial_summary = $stmt->fetch();
        
        $profit = $financial_summary['total_sales'] - $financial_summary['total_purchases'] - $financial_summary['total_expenses'];
        
        // حركات الخزنة
        $stmt = $pdo->prepare("
            SELECT 
                transaction_type,
                amount,
                description,
                created_at,
                CASE 
                    WHEN transaction_type = 'sale' THEN 'بيع'
                    WHEN transaction_type = 'purchase' THEN 'شراء'
                    WHEN transaction_type = 'expense' THEN 'مصروف'
                    WHEN transaction_type = 'income' THEN 'إيراد'
                    ELSE transaction_type
                END as transaction_type_ar
            FROM cash_register
            WHERE DATE(created_at) BETWEEN ? AND ?
            ORDER BY created_at DESC
            LIMIT 50
        ");
        $stmt->execute([$date_from, $date_to]);
        $cash_movements = $stmt->fetchAll();
        
        // المصروفات حسب النوع
        $stmt = $pdo->prepare("
            SELECT 
                category,
                SUM(amount) as total_amount,
                COUNT(*) as count
            FROM expenses
            WHERE expense_date BETWEEN ? AND ?
            GROUP BY category
            ORDER BY total_amount DESC
        ");
        $stmt->execute([$date_from, $date_to]);
        $expenses_by_category = $stmt->fetchAll();
        
    } catch (PDOException $e) {
        $error = 'خطأ في جلب التقارير المالية';
    }
}

// جلب تقارير العملاء
elseif ($report_type === 'customers') {
    try {
        // إحصائيات العملاء
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_customers,
                COUNT(CASE WHEN debt_balance > 0 THEN 1 END) as customers_with_debt,
                COUNT(CASE WHEN wallet_balance > 0 THEN 1 END) as customers_with_wallet,
                COALESCE(SUM(debt_balance), 0) as total_debts,
                COALESCE(SUM(wallet_balance), 0) as total_wallet
            FROM customers 
            WHERE is_active = 1
        ");
        $stmt->execute();
        $customers_summary = $stmt->fetch();
        
        // العملاء المدينون
        $stmt = $pdo->prepare("
            SELECT name, phone, debt_balance, wallet_balance, total_purchases
            FROM customers 
            WHERE is_active = 1 AND debt_balance > 0
            ORDER BY debt_balance DESC
        ");
        $stmt->execute();
        $customers_with_debt = $stmt->fetchAll();
        
        // العملاء الأكثر شراءً
        $stmt = $pdo->prepare("
            SELECT 
                c.name, c.phone,
                COUNT(s.id) as purchases_count,
                SUM(s.total_amount) as total_spent
            FROM customers c
            LEFT JOIN sales s ON c.id = s.customer_id AND s.sale_date BETWEEN ? AND ?
            WHERE c.is_active = 1
            GROUP BY c.id, c.name, c.phone
            HAVING purchases_count > 0
            ORDER BY total_spent DESC
            LIMIT 20
        ");
        $stmt->execute([$date_from, $date_to]);
        $top_customers_period = $stmt->fetchAll();
        
    } catch (PDOException $e) {
        $error = 'خطأ في جلب تقارير العملاء';
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - نظام إدارة المحل Z</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">التقارير</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                    </div>
                </div>
                
                <!-- فلاتر التقارير -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="get" class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">نوع التقرير</label>
                                <select class="form-select" name="type">
                                    <option value="sales" <?= $report_type === 'sales' ? 'selected' : '' ?>>تقرير المبيعات</option>
                                    <option value="inventory" <?= $report_type === 'inventory' ? 'selected' : '' ?>>تقرير المخزون</option>
                                    <option value="financial" <?= $report_type === 'financial' ? 'selected' : '' ?>>التقرير المالي</option>
                                    <option value="customers" <?= $report_type === 'customers' ? 'selected' : '' ?>>تقرير العملاء</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" name="date_from" value="<?= $date_from ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" name="date_to" value="<?= $date_to ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary d-block">
                                    <i class="fas fa-search"></i> عرض التقرير
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> <?= $error ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($report_type === 'sales' && isset($sales_summary)): ?>
                    <!-- تقرير المبيعات -->
                    <div class="row mb-4">
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                عدد الفواتير
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($sales_summary['total_invoices']) ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                إجمالي المبيعات
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($sales_summary['total_sales'], 2) ?> ج.م
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-info shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                المبلغ المحصل
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($sales_summary['total_paid'], 2) ?> ج.م
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-cash-register fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                الديون المستحقة
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($sales_summary['total_debts'], 2) ?> ج.م
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-lg-8">
                            <!-- المبيعات اليومية -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">المبيعات اليومية</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="dailySalesChart" height="100"></canvas>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4">
                            <!-- أفضل المنتجات -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">أفضل المنتجات مبيعاً</h6>
                                </div>
                                <div class="card-body">
                                    <?php if (empty($top_products)): ?>
                                        <p class="text-muted">لا توجد بيانات</p>
                                    <?php else: ?>
                                        <?php foreach ($top_products as $product): ?>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span><?= htmlspecialchars($product['name']) ?></span>
                                                <span class="fw-bold"><?= number_format($product['total_revenue'], 2) ?> ج.م</span>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أفضل العملاء -->
                    <?php if (!empty($top_customers)): ?>
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">أفضل العملاء</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>اسم العميل</th>
                                                <th>عدد المشتريات</th>
                                                <th>إجمالي الإنفاق</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($top_customers as $customer): ?>
                                                <tr>
                                                    <td><?= htmlspecialchars($customer['name']) ?></td>
                                                    <td><?= $customer['purchases_count'] ?></td>
                                                    <td><?= number_format($customer['total_spent'], 2) ?> ج.م</td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                <?php elseif ($report_type === 'inventory' && isset($inventory_summary)): ?>
                    <!-- تقرير المخزون -->
                    <div class="row mb-4">
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                إجمالي المنتجات
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($inventory_summary['total_products']) ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-boxes fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                مخزون منخفض
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($inventory_summary['low_stock_products']) ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-danger shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                                نفد المخزون
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($inventory_summary['out_of_stock_products']) ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                قيمة المخزون
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($inventory_summary['inventory_value'], 2) ?> ج.م
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- المنتجات منخفضة المخزون -->
                    <?php if (!empty($low_stock_products)): ?>
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">المنتجات منخفضة المخزون</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>اسم المنتج</th>
                                                <th>الكمية الحالية</th>
                                                <th>الحد الأدنى</th>
                                                <th>سعر التكلفة</th>
                                                <th>سعر البيع</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($low_stock_products as $product): ?>
                                                <tr>
                                                    <td><?= htmlspecialchars($product['name']) ?></td>
                                                    <td>
                                                        <span class="badge <?= $product['stock_quantity'] == 0 ? 'bg-danger' : 'bg-warning' ?>">
                                                            <?= $product['stock_quantity'] ?>
                                                        </span>
                                                    </td>
                                                    <td><?= $product['min_stock_level'] ?></td>
                                                    <td><?= number_format($product['cost_price'], 2) ?> ج.م</td>
                                                    <td><?= number_format($product['selling_price'], 2) ?> ج.م</td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                <?php elseif ($report_type === 'financial' && isset($financial_summary)): ?>
                    <!-- التقرير المالي -->
                    <div class="row mb-4">
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                إجمالي المبيعات
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($financial_summary['total_sales'], 2) ?> ج.م
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-arrow-up fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                إجمالي المشتريات
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($financial_summary['total_purchases'], 2) ?> ج.م
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-arrow-down fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-danger shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                                إجمالي المصروفات
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($financial_summary['total_expenses'], 2) ?> ج.م
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-minus fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-<?= $profit >= 0 ? 'success' : 'danger' ?> shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-<?= $profit >= 0 ? 'success' : 'danger' ?> text-uppercase mb-1">
                                                <?= $profit >= 0 ? 'الربح' : 'الخسارة' ?>
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format(abs($profit), 2) ?> ج.م
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-<?= $profit >= 0 ? 'chart-line' : 'chart-line-down' ?> fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- المصروفات حسب النوع -->
                    <?php if (!empty($expenses_by_category)): ?>
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">المصروفات حسب النوع</h6>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="expensesChart" height="200"></canvas>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-lg-6">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">تفاصيل المصروفات</h6>
                                    </div>
                                    <div class="card-body">
                                        <?php foreach ($expenses_by_category as $expense): ?>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span><?= htmlspecialchars($expense['category']) ?></span>
                                                <span class="fw-bold"><?= number_format($expense['total_amount'], 2) ?> ج.م</span>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                <?php elseif ($report_type === 'customers' && isset($customers_summary)): ?>
                    <!-- تقرير العملاء -->
                    <div class="row mb-4">
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                إجمالي العملاء
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($customers_summary['total_customers']) ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-users fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                عملاء مدينون
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($customers_summary['customers_with_debt']) ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-user-clock fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-danger shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                                إجمالي الديون
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($customers_summary['total_debts'], 2) ?> ج.م
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                الأرصدة المحفوظة
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($customers_summary['total_wallet'], 2) ?> ج.م
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-wallet fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- العملاء المدينون -->
                    <?php if (!empty($customers_with_debt)): ?>
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">العملاء المدينون</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>اسم العميل</th>
                                                <th>رقم الهاتف</th>
                                                <th>الدين</th>
                                                <th>الرصيد المحفوظ</th>
                                                <th>إجمالي المشتريات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($customers_with_debt as $customer): ?>
                                                <tr>
                                                    <td><?= htmlspecialchars($customer['name']) ?></td>
                                                    <td><?= htmlspecialchars($customer['phone']) ?></td>
                                                    <td class="text-danger fw-bold"><?= number_format($customer['debt_balance'], 2) ?> ج.م</td>
                                                    <td class="text-success"><?= number_format($customer['wallet_balance'], 2) ?> ج.م</td>
                                                    <td><?= number_format($customer['total_purchases'], 2) ?> ج.م</td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- أفضل العملاء في الفترة -->
                    <?php if (!empty($top_customers_period)): ?>
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">أفضل العملاء في الفترة المحددة</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>اسم العميل</th>
                                                <th>رقم الهاتف</th>
                                                <th>عدد المشتريات</th>
                                                <th>إجمالي الإنفاق</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($top_customers_period as $customer): ?>
                                                <tr>
                                                    <td><?= htmlspecialchars($customer['name']) ?></td>
                                                    <td><?= htmlspecialchars($customer['phone']) ?></td>
                                                    <td><?= $customer['purchases_count'] ?></td>
                                                    <td class="fw-bold"><?= number_format($customer['total_spent'], 2) ?> ج.م</td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        <?php if ($report_type === 'sales' && !empty($daily_sales)): ?>
            // رسم بياني للمبيعات اليومية
            const dailySalesCtx = document.getElementById('dailySalesChart').getContext('2d');
            new Chart(dailySalesCtx, {
                type: 'line',
                data: {
                    labels: <?= json_encode(array_reverse(array_column($daily_sales, 'sale_date'))) ?>,
                    datasets: [{
                        label: 'المبيعات اليومية',
                        data: <?= json_encode(array_reverse(array_column($daily_sales, 'daily_sales'))) ?>,
                        borderColor: 'rgb(102, 126, 234)',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        <?php endif; ?>
        
        <?php if ($report_type === 'financial' && !empty($expenses_by_category)): ?>
            // رسم بياني للمصروفات
            const expensesCtx = document.getElementById('expensesChart').getContext('2d');
            new Chart(expensesCtx, {
                type: 'doughnut',
                data: {
                    labels: <?= json_encode(array_column($expenses_by_category, 'category')) ?>,
                    datasets: [{
                        data: <?= json_encode(array_column($expenses_by_category, 'total_amount')) ?>,
                        backgroundColor: [
                            '#FF6384',
                            '#36A2EB',
                            '#FFCE56',
                            '#4BC0C0',
                            '#9966FF',
                            '#FF9F40'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        <?php endif; ?>
    </script>
</body>
</html>
