<?php
/**
 * نظام إدارة المحل Z - طباعة الفاتورة
 */

require_once 'config.php';
require_login();

$sale_id = $_GET['id'] ?? null;

if (!$sale_id) {
    die('رقم الفاتورة غير صحيح');
}

// جلب بيانات الفاتورة
try {
    $stmt = $pdo->prepare("
        SELECT s.*, c.name as customer_name, c.phone as customer_phone, c.address as customer_address,
               u.full_name as cashier_name
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
        LEFT JOIN users u ON s.created_by = u.id
        WHERE s.id = ?
    ");
    $stmt->execute([$sale_id]);
    $sale = $stmt->fetch();
    
    if (!$sale) {
        die('الفاتورة غير موجودة');
    }
    
    // جلب تفاصيل الفاتورة
    $stmt = $pdo->prepare("
        SELECT si.*, p.name as product_name, p.unit
        FROM sale_items si
        JOIN products p ON si.product_id = p.id
        WHERE si.sale_id = ?
        ORDER BY si.id
    ");
    $stmt->execute([$sale_id]);
    $sale_items = $stmt->fetchAll();
    
    // جلب إعدادات المتجر
    $stmt = $pdo->prepare("SELECT setting_name, setting_value FROM settings");
    $stmt->execute();
    $settings = [];
    while ($row = $stmt->fetch()) {
        $settings[$row['setting_name']] = $row['setting_value'];
    }
    
} catch (PDOException $e) {
    die('خطأ في جلب بيانات الفاتورة');
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة رقم <?= htmlspecialchars($sale['invoice_number']) ?></title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
            color: #333;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .invoice-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .store-info {
            margin-bottom: 10px;
        }
        
        .invoice-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-top: 15px;
        }
        
        .invoice-body {
            padding: 20px;
        }
        
        .invoice-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .invoice-info, .customer-info {
            flex: 1;
            min-width: 250px;
            margin-bottom: 15px;
        }
        
        .info-title {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }
        
        .info-item {
            margin-bottom: 5px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .items-table th {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
        }
        
        .items-table td {
            border: 1px solid #ddd;
            padding: 10px 8px;
            text-align: center;
        }
        
        .items-table tbody tr:nth-child(even) {
            background: #f9f9f9;
        }
        
        .totals-section {
            margin-top: 20px;
            border-top: 2px solid #667eea;
            padding-top: 15px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
        }
        
        .total-row.final {
            font-size: 1.2rem;
            font-weight: bold;
            color: #667eea;
            border-top: 1px solid #ddd;
            padding-top: 10px;
            margin-top: 10px;
        }
        
        .payment-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
        }
        
        .no-print {
            text-align: center;
            margin: 20px 0;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .no-print {
                display: none;
            }
            
            .invoice-container {
                border: none;
                border-radius: 0;
                box-shadow: none;
                max-width: none;
            }
            
            .invoice-header {
                background: #667eea !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
        }
        
        @media (max-width: 600px) {
            .invoice-details {
                flex-direction: column;
            }
            
            .items-table {
                font-size: 12px;
            }
            
            .items-table th,
            .items-table td {
                padding: 6px 4px;
            }
        }
    </style>
</head>
<body>
    <div class="no-print">
        <div style="text-align: center; margin-bottom: 20px;">
            <button onclick="window.print()" class="btn" style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 0 5px;">
                <i class="fas fa-print"></i> طباعة
            </button>
            <button onclick="window.close()" class="btn" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 0 5px;">
                <i class="fas fa-times"></i> إغلاق
            </button>
        </div>
    </div>
    
    <div class="invoice-container">
        <div class="invoice-header">
            <div class="logo">Z</div>
            <div class="store-info">
                <h2><?= htmlspecialchars($settings['store_name'] ?? 'محل Z') ?></h2>
                <?php if (!empty($settings['store_address'])): ?>
                    <div><?= htmlspecialchars($settings['store_address']) ?></div>
                <?php endif; ?>
                <?php if (!empty($settings['store_phone'])): ?>
                    <div>هاتف: <?= htmlspecialchars($settings['store_phone']) ?></div>
                <?php endif; ?>
                <?php if (!empty($settings['store_email'])): ?>
                    <div>إيميل: <?= htmlspecialchars($settings['store_email']) ?></div>
                <?php endif; ?>
            </div>
            <div class="invoice-title">فاتورة بيع</div>
        </div>
        
        <div class="invoice-body">
            <div class="invoice-details">
                <div class="invoice-info">
                    <div class="info-title">بيانات الفاتورة</div>
                    <div class="info-item"><strong>رقم الفاتورة:</strong> <?= htmlspecialchars($sale['invoice_number']) ?></div>
                    <div class="info-item"><strong>التاريخ:</strong> <?= date('Y-m-d', strtotime($sale['sale_date'])) ?></div>
                    <div class="info-item"><strong>الوقت:</strong> <?= date('H:i', strtotime($sale['created_at'])) ?></div>
                    <div class="info-item"><strong>الكاشير:</strong> <?= htmlspecialchars($sale['cashier_name']) ?></div>
                </div>
                
                <div class="customer-info">
                    <div class="info-title">بيانات العميل</div>
                    <?php if ($sale['customer_name']): ?>
                        <div class="info-item"><strong>الاسم:</strong> <?= htmlspecialchars($sale['customer_name']) ?></div>
                        <?php if ($sale['customer_phone']): ?>
                            <div class="info-item"><strong>الهاتف:</strong> <?= htmlspecialchars($sale['customer_phone']) ?></div>
                        <?php endif; ?>
                        <?php if ($sale['customer_address']): ?>
                            <div class="info-item"><strong>العنوان:</strong> <?= htmlspecialchars($sale['customer_address']) ?></div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="info-item">عميل نقدي</div>
                    <?php endif; ?>
                </div>
            </div>
            
            <table class="items-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>المنتج</th>
                        <th>الكمية</th>
                        <th>الوحدة</th>
                        <th>السعر</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($sale_items as $index => $item): ?>
                        <tr>
                            <td><?= $index + 1 ?></td>
                            <td><?= htmlspecialchars($item['product_name']) ?></td>
                            <td><?= $item['quantity'] ?></td>
                            <td><?= htmlspecialchars($item['unit']) ?></td>
                            <td><?= number_format($item['unit_price'], 2) ?> ج.م</td>
                            <td><?= number_format($item['total_price'], 2) ?> ج.م</td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <div class="totals-section">
                <?php
                $subtotal = array_sum(array_column($sale_items, 'total_price'));
                ?>
                <div class="total-row">
                    <span>المجموع الفرعي:</span>
                    <span><?= number_format($subtotal, 2) ?> ج.م</span>
                </div>
                
                <?php if ($sale['discount_amount'] > 0): ?>
                    <div class="total-row">
                        <span>الخصم:</span>
                        <span><?= number_format($sale['discount_amount'], 2) ?> ج.م</span>
                    </div>
                <?php endif; ?>
                
                <div class="total-row final">
                    <span>الإجمالي النهائي:</span>
                    <span><?= number_format($sale['total_amount'], 2) ?> ج.م</span>
                </div>
            </div>
            
            <div class="payment-info">
                <div class="info-title">تفاصيل الدفع</div>
                <div class="total-row">
                    <span>طريقة الدفع:</span>
                    <span>
                        <?php
                        $payment_types = [
                            'cash' => 'نقدي',
                            'credit' => 'آجل',
                            'wallet' => 'رصيد محفوظ',
                            'mixed' => 'مختلط'
                        ];
                        echo $payment_types[$sale['payment_type']] ?? $sale['payment_type'];
                        ?>
                    </span>
                </div>
                
                <?php if ($sale['paid_amount'] > 0): ?>
                    <div class="total-row">
                        <span>المبلغ المدفوع نقداً:</span>
                        <span><?= number_format($sale['paid_amount'], 2) ?> ج.م</span>
                    </div>
                <?php endif; ?>
                
                <?php if ($sale['wallet_used'] > 0): ?>
                    <div class="total-row">
                        <span>المستخدم من الرصيد:</span>
                        <span><?= number_format($sale['wallet_used'], 2) ?> ج.م</span>
                    </div>
                <?php endif; ?>
                
                <?php if ($sale['change_amount'] > 0): ?>
                    <div class="total-row" style="color: #28a745;">
                        <span>الفكة:</span>
                        <span><?= number_format($sale['change_amount'], 2) ?> ج.م</span>
                    </div>
                <?php endif; ?>
                
                <?php if ($sale['remaining_amount'] > 0): ?>
                    <div class="total-row" style="color: #dc3545;">
                        <span>المبلغ المتبقي (دين):</span>
                        <span><?= number_format($sale['remaining_amount'], 2) ?> ج.م</span>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="footer">
                <p><?= htmlspecialchars($settings['receipt_footer'] ?? 'شكراً لتعاملكم معنا') ?></p>
                <p>تاريخ الطباعة: <?= date('Y-m-d H:i:s') ?></p>
            </div>
        </div>
    </div>
    
    <script>
        // طباعة تلقائية عند فتح الصفحة (اختياري)
        // window.onload = function() { window.print(); };
    </script>
</body>
</html>
