<?php
/**
 * نظام إدارة المحل Z - الصفحة الرئيسية
 */

require_once 'config.php';
require_login();

// جلب إحصائيات سريعة
try {
    // إجمالي المبيعات اليوم
    $stmt = $pdo->prepare("SELECT COALESCE(SUM(total_amount), 0) as today_sales FROM sales WHERE DATE(created_at) = CURDATE()");
    $stmt->execute();
    $today_sales = $stmt->fetchColumn();
    
    // عدد المنتجات
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE is_active = 1");
    $stmt->execute();
    $total_products = $stmt->fetchColumn();
    
    // عدد العملاء
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM customers WHERE is_active = 1");
    $stmt->execute();
    $total_customers = $stmt->fetchColumn();
    
    // رصيد الخزنة
    $stmt = $pdo->prepare("SELECT balance_after FROM cash_register ORDER BY id DESC LIMIT 1");
    $stmt->execute();
    $cash_balance = $stmt->fetchColumn() ?: 0;
    
    // المنتجات منخفضة المخزون
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE stock_quantity <= min_stock_level AND is_active = 1");
    $stmt->execute();
    $low_stock_count = $stmt->fetchColumn();
    
    // إجمالي الديون
    $stmt = $pdo->prepare("SELECT COALESCE(SUM(debt_balance), 0) FROM customers WHERE debt_balance > 0");
    $stmt->execute();
    $total_debts = $stmt->fetchColumn();
    
} catch (PDOException $e) {
    $today_sales = $total_products = $total_customers = $cash_balance = $low_stock_count = $total_debts = 0;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المحل Z</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">لوحة التحكم</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-calendar"></i> اليوم
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- البطاقات الإحصائية -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-right-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col me-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            مبيعات اليوم
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?= number_format($today_sales, 2) ?> ج.م
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-cash-register fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-right-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col me-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            رصيد الخزنة
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?= number_format($cash_balance, 2) ?> ج.م
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-wallet fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-right-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col me-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            عدد المنتجات
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?= number_format($total_products) ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-boxes fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-right-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col me-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            عدد العملاء
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?= number_format($total_customers) ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-users fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- التنبيهات -->
                <?php if ($low_stock_count > 0): ?>
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>تنبيه!</strong> يوجد <?= $low_stock_count ?> منتج بمخزون منخفض.
                    <a href="products.php?filter=low_stock" class="alert-link">عرض المنتجات</a>
                </div>
                <?php endif; ?>
                
                <?php if ($total_debts > 0): ?>
                <div class="alert alert-info" role="alert">
                    <i class="fas fa-info-circle"></i>
                    إجمالي الديون المستحقة: <strong><?= number_format($total_debts, 2) ?> ج.م</strong>
                    <a href="customers.php?filter=debts" class="alert-link">عرض العملاء المدينين</a>
                </div>
                <?php endif; ?>
                
                <!-- الإجراءات السريعة -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">الإجراءات السريعة</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <a href="pos.php" class="btn btn-primary btn-lg w-100">
                                            <i class="fas fa-cash-register fa-2x mb-2"></i><br>
                                            نقطة البيع
                                        </a>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <a href="products.php?action=add" class="btn btn-success btn-lg w-100">
                                            <i class="fas fa-plus-circle fa-2x mb-2"></i><br>
                                            إضافة منتج
                                        </a>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <a href="customers.php?action=add" class="btn btn-info btn-lg w-100">
                                            <i class="fas fa-user-plus fa-2x mb-2"></i><br>
                                            إضافة عميل
                                        </a>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <a href="purchases.php?action=add" class="btn btn-warning btn-lg w-100">
                                            <i class="fas fa-shopping-cart fa-2x mb-2"></i><br>
                                            مشتريات جديدة
                                        </a>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <a href="reports.php" class="btn btn-secondary btn-lg w-100">
                                            <i class="fas fa-chart-bar fa-2x mb-2"></i><br>
                                            التقارير
                                        </a>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <a href="barcode.php" class="btn btn-dark btn-lg w-100">
                                            <i class="fas fa-barcode fa-2x mb-2"></i><br>
                                            إنتاج باركود
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">آخر المبيعات</h6>
                            </div>
                            <div class="card-body">
                                <?php
                                try {
                                    $stmt = $pdo->prepare("
                                        SELECT s.invoice_number, s.total_amount, s.created_at, c.name as customer_name
                                        FROM sales s
                                        LEFT JOIN customers c ON s.customer_id = c.id
                                        ORDER BY s.created_at DESC
                                        LIMIT 5
                                    ");
                                    $stmt->execute();
                                    $recent_sales = $stmt->fetchAll();
                                    
                                    if ($recent_sales):
                                        foreach ($recent_sales as $sale):
                                ?>
                                <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                                    <div>
                                        <div class="font-weight-bold"><?= htmlspecialchars($sale['invoice_number']) ?></div>
                                        <small class="text-muted"><?= htmlspecialchars($sale['customer_name'] ?: 'عميل نقدي') ?></small>
                                    </div>
                                    <div class="text-end">
                                        <div class="font-weight-bold"><?= number_format($sale['total_amount'], 2) ?> ج.م</div>
                                        <small class="text-muted"><?= date('H:i', strtotime($sale['created_at'])) ?></small>
                                    </div>
                                </div>
                                <?php
                                        endforeach;
                                    else:
                                ?>
                                <p class="text-muted text-center">لا توجد مبيعات حتى الآن</p>
                                <?php endif; ?>
                                <div class="text-center mt-3">
                                    <a href="sales.php" class="btn btn-sm btn-outline-primary">عرض جميع المبيعات</a>
                                </div>
                                <?php } catch (PDOException $e) { ?>
                                <p class="text-danger">خطأ في جلب البيانات</p>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
