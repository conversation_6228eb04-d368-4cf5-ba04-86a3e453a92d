<?php
/**
 * نظام إدارة المحل Z - ملف الإعدادات الرئيسي
 */

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'store_z');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات النظام
define('SITE_NAME', 'نظام إدارة المحل Z');
define('SITE_VERSION', '1.0.0');
define('TIMEZONE', 'Africa/Cairo');

// تعيين المنطقة الزمنية
date_default_timezone_set(TIMEZONE);

// إعدادات العرض
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// الاتصال بقاعدة البيانات
try {
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ];
    
    $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
    
} catch (PDOException $e) {
    // إذا لم تكن قاعدة البيانات موجودة، حاول إنشاؤها
    if ($e->getCode() == 1049) {
        try {
            $dsn_without_db = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
            $pdo_temp = new PDO($dsn_without_db, DB_USER, DB_PASS, $options);
            $pdo_temp->exec("CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            
            // الاتصال بقاعدة البيانات الجديدة
            $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
            
        } catch (PDOException $e2) {
            die("خطأ في الاتصال بقاعدة البيانات: " . $e2->getMessage());
        }
    } else {
        die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
    }
}

/**
 * دالة التحقق من تسجيل الدخول
 */
function require_login() {
    if (!isset($_SESSION['user_id'])) {
        header('Location: login.php');
        exit;
    }
}

/**
 * دالة التحقق من صلاحيات المدير
 */
function require_admin() {
    require_login();
    if ($_SESSION['role'] !== 'admin') {
        die('غير مسموح لك بالوصول لهذه الصفحة');
    }
}

/**
 * دالة تنظيف البيانات
 */
function clean_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * دالة إنشاء رقم فاتورة عشوائي
 */
function generate_invoice_number($prefix = 'INV') {
    return $prefix . '-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
}

/**
 * دالة إنشاء باركود
 */
function generate_barcode() {
    return date('Ymd') . str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT);
}

/**
 * دالة تحويل التاريخ للعربية
 */
function arabic_date($date) {
    $months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $timestamp = strtotime($date);
    $day = date('d', $timestamp);
    $month = $months[(int)date('m', $timestamp)];
    $year = date('Y', $timestamp);
    
    return "$day $month $year";
}

/**
 * دالة تنسيق الأرقام
 */
function format_number($number, $decimals = 2) {
    return number_format($number, $decimals, '.', ',');
}

/**
 * دالة إرسال رسالة واتساب
 */
function send_whatsapp_message($phone, $message) {
    // تنظيف رقم الهاتف
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // إضافة رمز الدولة إذا لم يكن موجوداً
    if (!str_starts_with($phone, '20')) {
        $phone = '20' . $phone;
    }
    
    // إنشاء رابط الواتساب
    $whatsapp_url = "https://wa.me/$phone?text=" . urlencode($message);
    
    return $whatsapp_url;
}

/**
 * دالة جلب إعدادات النظام
 */
function get_setting($name, $default = null) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_name = ?");
        $stmt->execute([$name]);
        $result = $stmt->fetchColumn();
        
        return $result !== false ? $result : $default;
    } catch (PDOException $e) {
        return $default;
    }
}

/**
 * دالة حفظ إعدادات النظام
 */
function save_setting($name, $value) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO settings (setting_name, setting_value) 
            VALUES (?, ?) 
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
        ");
        return $stmt->execute([$name, $value]);
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * دالة تسجيل الأنشطة
 */
function log_activity($action, $details = '', $user_id = null) {
    global $pdo;
    
    if ($user_id === null && isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
    }
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO activity_log (user_id, action, details, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?)
        ");
        return $stmt->execute([
            $user_id,
            $action,
            $details,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    } catch (PDOException $e) {
        // تجاهل الأخطاء في تسجيل الأنشطة
        return false;
    }
}

/**
 * دالة التحقق من وجود الجداول
 */
function check_installation() {
    global $pdo;
    
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * دالة إعادة التوجيه
 */
function redirect($url, $message = '', $type = 'success') {
    if ($message) {
        $_SESSION['flash_message'] = $message;
        $_SESSION['flash_type'] = $type;
    }
    header("Location: $url");
    exit;
}

/**
 * دالة عرض الرسائل المؤقتة
 */
function show_flash_message() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'success';
        
        unset($_SESSION['flash_message']);
        unset($_SESSION['flash_type']);
        
        $alert_class = $type === 'error' ? 'alert-danger' : 'alert-success';
        $icon = $type === 'error' ? 'fas fa-exclamation-triangle' : 'fas fa-check-circle';
        
        echo "<div class='alert $alert_class alert-dismissible fade show'>
                <i class='$icon'></i> " . htmlspecialchars($message) . "
                <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
              </div>";
    }
}

/**
 * دالة التحقق من صحة البريد الإلكتروني
 */
function is_valid_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * دالة التحقق من صحة رقم الهاتف
 */
function is_valid_phone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    return strlen($phone) >= 10 && strlen($phone) <= 15;
}

/**
 * دالة حساب النسبة المئوية
 */
function calculate_percentage($part, $total) {
    if ($total == 0) return 0;
    return round(($part / $total) * 100, 2);
}

/**
 * دالة تحويل الحجم بالبايت
 */
function format_bytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

// التحقق من التثبيت
if (!check_installation() && basename($_SERVER['PHP_SELF']) !== 'install.php') {
    header('Location: install.php');
    exit;
}
?>
