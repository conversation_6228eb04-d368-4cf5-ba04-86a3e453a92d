<?php
/**
 * نظام إدارة المحل Z - الإعدادات
 */

require_once 'config.php';
require_login();

// التحقق من صلاحيات المدير
if ($_SESSION['role'] !== 'admin') {
    die('غير مسموح لك بالوصول لهذه الصفحة');
}

$message = '';
$error = '';

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action']) && $_POST['action'] === 'save_settings') {
            $settings = [
                'store_name' => trim($_POST['store_name']),
                'store_address' => trim($_POST['store_address']),
                'store_phone' => trim($_POST['store_phone']),
                'store_email' => trim($_POST['store_email']),
                'currency' => trim($_POST['currency']),
                'tax_rate' => floatval($_POST['tax_rate']),
                'receipt_footer' => trim($_POST['receipt_footer']),
                'low_stock_alert' => isset($_POST['low_stock_alert']) ? '1' : '0',
                'backup_frequency' => $_POST['backup_frequency']
            ];
            
            // حفظ الإعدادات
            foreach ($settings as $name => $value) {
                $stmt = $pdo->prepare("
                    INSERT INTO settings (setting_name, setting_value) 
                    VALUES (?, ?) 
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                ");
                $stmt->execute([$name, $value]);
            }
            
            $message = 'تم حفظ الإعدادات بنجاح';
            
        } elseif (isset($_POST['action']) && $_POST['action'] === 'change_password') {
            $current_password = $_POST['current_password'];
            $new_password = $_POST['new_password'];
            $confirm_password = $_POST['confirm_password'];
            
            // التحقق من كلمة المرور الحالية
            $stmt = $pdo->prepare("SELECT password FROM users WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $user = $stmt->fetch();
            
            if (!password_verify($current_password, $user['password'])) {
                throw new Exception('كلمة المرور الحالية غير صحيحة');
            }
            
            if ($new_password !== $confirm_password) {
                throw new Exception('كلمة المرور الجديدة وتأكيدها غير متطابقين');
            }
            
            if (strlen($new_password) < 6) {
                throw new Exception('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            }
            
            // تحديث كلمة المرور
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
            $stmt->execute([$hashed_password, $_SESSION['user_id']]);
            
            $message = 'تم تغيير كلمة المرور بنجاح';
            
        } elseif (isset($_POST['action']) && $_POST['action'] === 'add_user') {
            $username = trim($_POST['username']);
            $full_name = trim($_POST['full_name']);
            $email = trim($_POST['email']);
            $password = $_POST['password'];
            $role = $_POST['role'];
            
            // التحقق من عدم وجود اسم المستخدم
            $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
            $stmt->execute([$username]);
            if ($stmt->fetch()) {
                throw new Exception('اسم المستخدم موجود بالفعل');
            }
            
            // إضافة المستخدم الجديد
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("
                INSERT INTO users (username, password, full_name, email, role) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$username, $hashed_password, $full_name, $email, $role]);
            
            $message = 'تم إضافة المستخدم بنجاح';
            
        } elseif (isset($_POST['action']) && $_POST['action'] === 'delete_user') {
            $user_id = $_POST['user_id'];
            
            if ($user_id == $_SESSION['user_id']) {
                throw new Exception('لا يمكنك حذف حسابك الخاص');
            }
            
            $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            
            $message = 'تم حذف المستخدم بنجاح';
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// جلب الإعدادات الحالية
$stmt = $pdo->prepare("SELECT setting_name, setting_value FROM settings");
$stmt->execute();
$current_settings = [];
while ($row = $stmt->fetch()) {
    $current_settings[$row['setting_name']] = $row['setting_value'];
}

// الإعدادات الافتراضية
$default_settings = [
    'store_name' => 'محل Z',
    'store_address' => '',
    'store_phone' => '',
    'store_email' => '',
    'currency' => 'ج.م',
    'tax_rate' => '0',
    'receipt_footer' => 'شكراً لتعاملكم معنا',
    'low_stock_alert' => '1',
    'backup_frequency' => 'daily'
];

// دمج الإعدادات
$settings = array_merge($default_settings, $current_settings);

// جلب المستخدمين
$stmt = $pdo->prepare("SELECT * FROM users ORDER BY created_at DESC");
$stmt->execute();
$users = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - نظام إدارة المحل Z</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">الإعدادات</h1>
                </div>
                
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- تبويبات الإعدادات -->
                <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="store-tab" data-bs-toggle="tab" data-bs-target="#store" type="button" role="tab">
                            <i class="fas fa-store"></i> إعدادات المتجر
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="account-tab" data-bs-toggle="tab" data-bs-target="#account" type="button" role="tab">
                            <i class="fas fa-user"></i> الحساب الشخصي
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab">
                            <i class="fas fa-users"></i> إدارة المستخدمين
                        </button>
                    </li>
                </ul>
                
                <div class="tab-content" id="settingsTabsContent">
                    <!-- إعدادات المتجر -->
                    <div class="tab-pane fade show active" id="store" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">إعدادات المتجر</h5>
                            </div>
                            <div class="card-body">
                                <form method="post">
                                    <input type="hidden" name="action" value="save_settings">
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">اسم المتجر</label>
                                                <input type="text" class="form-control" name="store_name" 
                                                       value="<?= htmlspecialchars($settings['store_name']) ?>">
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">رقم الهاتف</label>
                                                <input type="tel" class="form-control" name="store_phone" 
                                                       value="<?= htmlspecialchars($settings['store_phone']) ?>">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">البريد الإلكتروني</label>
                                                <input type="email" class="form-control" name="store_email" 
                                                       value="<?= htmlspecialchars($settings['store_email']) ?>">
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">العملة</label>
                                                <input type="text" class="form-control" name="currency" 
                                                       value="<?= htmlspecialchars($settings['currency']) ?>">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">عنوان المتجر</label>
                                        <textarea class="form-control" name="store_address" rows="3"><?= htmlspecialchars($settings['store_address']) ?></textarea>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">معدل الضريبة (%)</label>
                                                <input type="number" class="form-control" name="tax_rate" 
                                                       step="0.01" min="0" max="100"
                                                       value="<?= htmlspecialchars($settings['tax_rate']) ?>">
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">تكرار النسخ الاحتياطي</label>
                                                <select class="form-select" name="backup_frequency">
                                                    <option value="daily" <?= $settings['backup_frequency'] === 'daily' ? 'selected' : '' ?>>يومي</option>
                                                    <option value="weekly" <?= $settings['backup_frequency'] === 'weekly' ? 'selected' : '' ?>>أسبوعي</option>
                                                    <option value="monthly" <?= $settings['backup_frequency'] === 'monthly' ? 'selected' : '' ?>>شهري</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">نص ذيل الفاتورة</label>
                                        <textarea class="form-control" name="receipt_footer" rows="2"><?= htmlspecialchars($settings['receipt_footer']) ?></textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="low_stock_alert" 
                                                   <?= $settings['low_stock_alert'] === '1' ? 'checked' : '' ?>>
                                            <label class="form-check-label">
                                                تفعيل تنبيهات المخزون المنخفض
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> حفظ الإعدادات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الحساب الشخصي -->
                    <div class="tab-pane fade" id="account" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">تغيير كلمة المرور</h5>
                            </div>
                            <div class="card-body">
                                <form method="post" class="needs-validation" novalidate>
                                    <input type="hidden" name="action" value="change_password">
                                    
                                    <div class="mb-3">
                                        <label class="form-label">كلمة المرور الحالية</label>
                                        <input type="password" class="form-control" name="current_password" required>
                                        <div class="invalid-feedback">يرجى إدخال كلمة المرور الحالية</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">كلمة المرور الجديدة</label>
                                        <input type="password" class="form-control" name="new_password" minlength="6" required>
                                        <div class="invalid-feedback">كلمة المرور يجب أن تكون 6 أحرف على الأقل</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">تأكيد كلمة المرور الجديدة</label>
                                        <input type="password" class="form-control" name="confirm_password" minlength="6" required>
                                        <div class="invalid-feedback">يرجى تأكيد كلمة المرور</div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-key"></i> تغيير كلمة المرور
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- إدارة المستخدمين -->
                    <div class="tab-pane fade" id="users" role="tabpanel">
                        <div class="row mt-3">
                            <div class="col-lg-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">قائمة المستخدمين</h5>
                                    </div>
                                    <div class="card-body p-0">
                                        <div class="table-responsive">
                                            <table class="table table-hover mb-0">
                                                <thead>
                                                    <tr>
                                                        <th>اسم المستخدم</th>
                                                        <th>الاسم الكامل</th>
                                                        <th>البريد الإلكتروني</th>
                                                        <th>الدور</th>
                                                        <th>تاريخ الإنشاء</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($users as $user): ?>
                                                        <tr>
                                                            <td><?= htmlspecialchars($user['username']) ?></td>
                                                            <td><?= htmlspecialchars($user['full_name']) ?></td>
                                                            <td><?= htmlspecialchars($user['email']) ?></td>
                                                            <td>
                                                                <span class="badge <?= $user['role'] === 'admin' ? 'bg-danger' : 'bg-primary' ?>">
                                                                    <?= $user['role'] === 'admin' ? 'مدير' : 'مستخدم' ?>
                                                                </span>
                                                            </td>
                                                            <td><?= date('Y-m-d', strtotime($user['created_at'])) ?></td>
                                                            <td>
                                                                <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                                                    <button type="button" class="btn btn-sm btn-danger" 
                                                                            onclick="deleteUser(<?= $user['id'] ?>, '<?= htmlspecialchars($user['username']) ?>')">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                <?php else: ?>
                                                                    <span class="text-muted">الحساب الحالي</span>
                                                                <?php endif; ?>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-lg-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">إضافة مستخدم جديد</h5>
                                    </div>
                                    <div class="card-body">
                                        <form method="post" class="needs-validation" novalidate>
                                            <input type="hidden" name="action" value="add_user">
                                            
                                            <div class="mb-3">
                                                <label class="form-label">اسم المستخدم</label>
                                                <input type="text" class="form-control" name="username" required>
                                                <div class="invalid-feedback">يرجى إدخال اسم المستخدم</div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label class="form-label">الاسم الكامل</label>
                                                <input type="text" class="form-control" name="full_name" required>
                                                <div class="invalid-feedback">يرجى إدخال الاسم الكامل</div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label class="form-label">البريد الإلكتروني</label>
                                                <input type="email" class="form-control" name="email">
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label class="form-label">كلمة المرور</label>
                                                <input type="password" class="form-control" name="password" minlength="6" required>
                                                <div class="invalid-feedback">كلمة المرور يجب أن تكون 6 أحرف على الأقل</div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label class="form-label">الدور</label>
                                                <select class="form-select" name="role" required>
                                                    <option value="user">مستخدم</option>
                                                    <option value="admin">مدير</option>
                                                </select>
                                            </div>
                                            
                                            <button type="submit" class="btn btn-success w-100">
                                                <i class="fas fa-user-plus"></i> إضافة المستخدم
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function deleteUser(userId, username) {
            if (confirm(`هل أنت متأكد من حذف المستخدم "${username}"؟`)) {
                const form = document.createElement('form');
                form.method = 'post';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_user">
                    <input type="hidden" name="user_id" value="${userId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        // تفعيل التحقق من صحة النماذج
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
        
        // التحقق من تطابق كلمات المرور
        document.querySelector('input[name="confirm_password"]')?.addEventListener('input', function() {
            const newPassword = document.querySelector('input[name="new_password"]').value;
            const confirmPassword = this.value;
            
            if (newPassword !== confirmPassword) {
                this.setCustomValidity('كلمات المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
