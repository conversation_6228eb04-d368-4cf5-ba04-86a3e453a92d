<?php
/**
 * نظام إدارة المحل Z - إدارة المشتريات
 */

require_once 'config.php';
require_login();

$action = $_GET['action'] ?? 'list';
$purchase_id = $_GET['id'] ?? null;
$message = '';
$error = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if ($action === 'add' || $action === 'edit') {
            $pdo->beginTransaction();
            
            $supplier_id = $_POST['supplier_id'] ?: null;
            $invoice_number = trim($_POST['invoice_number']);
            $purchase_date = $_POST['purchase_date'];
            $paid_amount = floatval($_POST['paid_amount']);
            $notes = trim($_POST['notes']);
            $items = json_decode($_POST['items'], true);
            
            if (empty($items)) {
                throw new Exception('يجب إضافة منتجات للفاتورة');
            }
            
            // حساب الإجمالي
            $total_amount = 0;
            foreach ($items as $item) {
                $total_amount += $item['quantity'] * $item['cost_price'];
            }
            
            $remaining_amount = $total_amount - $paid_amount;
            $payment_status = $remaining_amount > 0 ? ($paid_amount > 0 ? 'partial' : 'unpaid') : 'paid';
            
            if ($action === 'add') {
                // إضافة فاتورة مشتريات جديدة
                $stmt = $pdo->prepare("
                    INSERT INTO purchases (invoice_number, supplier_id, total_amount, paid_amount, 
                                         remaining_amount, payment_status, notes, purchase_date, created_by) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $invoice_number, $supplier_id, $total_amount, $paid_amount,
                    $remaining_amount, $payment_status, $notes, $purchase_date, $_SESSION['user_id']
                ]);
                
                $purchase_id = $pdo->lastInsertId();
                
                // إضافة تفاصيل المشتريات وتحديث المخزون
                foreach ($items as $item) {
                    // إضافة تفاصيل المشتريات
                    $stmt = $pdo->prepare("
                        INSERT INTO purchase_items (purchase_id, product_id, quantity, cost_price, total_price) 
                        VALUES (?, ?, ?, ?, ?)
                    ");
                    $total_price = $item['quantity'] * $item['cost_price'];
                    $stmt->execute([$purchase_id, $item['product_id'], $item['quantity'], $item['cost_price'], $total_price]);
                    
                    // تحديث المخزون وسعر التكلفة
                    $stmt = $pdo->prepare("
                        UPDATE products 
                        SET stock_quantity = stock_quantity + ?, cost_price = ? 
                        WHERE id = ?
                    ");
                    $stmt->execute([$item['quantity'], $item['cost_price'], $item['product_id']]);
                    
                    // تسجيل حركة المخزون
                    $stmt = $pdo->prepare("
                        INSERT INTO stock_movements (product_id, movement_type, quantity, reference_type, reference_id, created_by) 
                        VALUES (?, 'in', ?, 'purchase', ?, ?)
                    ");
                    $stmt->execute([$item['product_id'], $item['quantity'], $purchase_id, $_SESSION['user_id']]);
                }
                
                // تحديث رصيد المورد
                if ($supplier_id && $remaining_amount > 0) {
                    $stmt = $pdo->prepare("UPDATE suppliers SET balance = balance + ? WHERE id = ?");
                    $stmt->execute([$remaining_amount, $supplier_id]);
                }
                
                // تحديث الخزنة
                if ($paid_amount > 0) {
                    $stmt = $pdo->prepare("SELECT balance_after FROM cash_register ORDER BY id DESC LIMIT 1");
                    $stmt->execute();
                    $current_balance = $stmt->fetchColumn() ?: 0;
                    
                    $stmt = $pdo->prepare("
                        INSERT INTO cash_register (transaction_type, amount, balance_before, balance_after, 
                                                 reference_type, reference_id, description, created_by) 
                        VALUES ('purchase', ?, ?, ?, 'purchase', ?, ?, ?)
                    ");
                    $stmt->execute([
                        $paid_amount, $current_balance, $current_balance - $paid_amount,
                        $purchase_id, "مشتريات - فاتورة رقم: $invoice_number", $_SESSION['user_id']
                    ]);
                }
                
                $message = 'تم إضافة فاتورة المشتريات بنجاح';
                
            } else {
                // تعديل فاتورة موجودة (محدود)
                $stmt = $pdo->prepare("
                    UPDATE purchases 
                    SET supplier_id = ?, notes = ?, purchase_date = ? 
                    WHERE id = ?
                ");
                $stmt->execute([$supplier_id, $notes, $purchase_date, $purchase_id]);
                
                $message = 'تم تحديث فاتورة المشتريات بنجاح';
            }
            
            $pdo->commit();
            
        } elseif ($action === 'delete') {
            $purchase_id = $_POST['purchase_id'];
            
            $pdo->beginTransaction();
            
            // الحصول على تفاصيل الفاتورة
            $stmt = $pdo->prepare("SELECT * FROM purchases WHERE id = ?");
            $stmt->execute([$purchase_id]);
            $purchase = $stmt->fetch();
            
            if ($purchase) {
                // الحصول على تفاصيل المنتجات
                $stmt = $pdo->prepare("SELECT * FROM purchase_items WHERE purchase_id = ?");
                $stmt->execute([$purchase_id]);
                $items = $stmt->fetchAll();
                
                // إرجاع المخزون
                foreach ($items as $item) {
                    $stmt = $pdo->prepare("
                        UPDATE products 
                        SET stock_quantity = stock_quantity - ? 
                        WHERE id = ?
                    ");
                    $stmt->execute([$item['quantity'], $item['product_id']]);
                    
                    // تسجيل حركة المخزون
                    $stmt = $pdo->prepare("
                        INSERT INTO stock_movements (product_id, movement_type, quantity, reference_type, reference_id, notes, created_by) 
                        VALUES (?, 'out', ?, 'adjustment', ?, 'حذف فاتورة مشتريات', ?)
                    ");
                    $stmt->execute([$item['product_id'], $item['quantity'], $purchase_id, $_SESSION['user_id']]);
                }
                
                // تحديث رصيد المورد
                if ($purchase['supplier_id'] && $purchase['remaining_amount'] > 0) {
                    $stmt = $pdo->prepare("UPDATE suppliers SET balance = balance - ? WHERE id = ?");
                    $stmt->execute([$purchase['remaining_amount'], $purchase['supplier_id']]);
                }
                
                // حذف التفاصيل والفاتورة
                $stmt = $pdo->prepare("DELETE FROM purchase_items WHERE purchase_id = ?");
                $stmt->execute([$purchase_id]);
                
                $stmt = $pdo->prepare("DELETE FROM purchases WHERE id = ?");
                $stmt->execute([$purchase_id]);
                
                $message = 'تم حذف فاتورة المشتريات';
            }
            
            $pdo->commit();
            
        } elseif ($action === 'pay') {
            $purchase_id = $_POST['purchase_id'];
            $payment_amount = floatval($_POST['payment_amount']);
            
            if ($payment_amount <= 0) {
                throw new Exception('يرجى إدخال مبلغ صحيح');
            }
            
            // الحصول على بيانات الفاتورة
            $stmt = $pdo->prepare("SELECT * FROM purchases WHERE id = ?");
            $stmt->execute([$purchase_id]);
            $purchase = $stmt->fetch();
            
            if ($purchase && $payment_amount <= $purchase['remaining_amount']) {
                $new_paid = $purchase['paid_amount'] + $payment_amount;
                $new_remaining = $purchase['remaining_amount'] - $payment_amount;
                $new_status = $new_remaining > 0 ? 'partial' : 'paid';
                
                // تحديث الفاتورة
                $stmt = $pdo->prepare("
                    UPDATE purchases 
                    SET paid_amount = ?, remaining_amount = ?, payment_status = ? 
                    WHERE id = ?
                ");
                $stmt->execute([$new_paid, $new_remaining, $new_status, $purchase_id]);
                
                // تحديث رصيد المورد
                if ($purchase['supplier_id']) {
                    $stmt = $pdo->prepare("UPDATE suppliers SET balance = balance - ? WHERE id = ?");
                    $stmt->execute([$payment_amount, $purchase['supplier_id']]);
                }
                
                // تحديث الخزنة
                $stmt = $pdo->prepare("SELECT balance_after FROM cash_register ORDER BY id DESC LIMIT 1");
                $stmt->execute();
                $current_balance = $stmt->fetchColumn() ?: 0;
                
                $stmt = $pdo->prepare("
                    INSERT INTO cash_register (transaction_type, amount, balance_before, balance_after, 
                                             reference_type, reference_id, description, created_by) 
                    VALUES ('expense', ?, ?, ?, 'purchase', ?, ?, ?)
                ");
                $stmt->execute([
                    $payment_amount, $current_balance, $current_balance - $payment_amount,
                    $purchase_id, "دفع مشتريات - فاتورة رقم: {$purchase['invoice_number']}", $_SESSION['user_id']
                ]);
                
                $message = 'تم تسجيل الدفعة بنجاح';
            } else {
                throw new Exception('مبلغ الدفع غير صحيح');
            }
        }
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $error = $e->getMessage();
    }
}

// جلب البيانات للعرض
if ($action === 'edit' && $purchase_id) {
    $stmt = $pdo->prepare("
        SELECT p.*, s.name as supplier_name 
        FROM purchases p 
        LEFT JOIN suppliers s ON p.supplier_id = s.id 
        WHERE p.id = ?
    ");
    $stmt->execute([$purchase_id]);
    $purchase = $stmt->fetch();
    
    if (!$purchase) {
        $error = 'فاتورة المشتريات غير موجودة';
        $action = 'list';
    } else {
        // جلب تفاصيل الفاتورة
        $stmt = $pdo->prepare("
            SELECT pi.*, pr.name as product_name 
            FROM purchase_items pi 
            JOIN products pr ON pi.product_id = pr.id 
            WHERE pi.purchase_id = ?
        ");
        $stmt->execute([$purchase_id]);
        $purchase_items = $stmt->fetchAll();
    }
}

// جلب المشتريات للعرض
if ($action === 'list') {
    $search = $_GET['search'] ?? '';
    $supplier_filter = $_GET['supplier'] ?? '';
    $status_filter = $_GET['status'] ?? '';
    
    $sql = "
        SELECT p.*, s.name as supplier_name 
        FROM purchases p 
        LEFT JOIN suppliers s ON p.supplier_id = s.id 
        WHERE 1=1
    ";
    $params = [];
    
    if ($search) {
        $sql .= " AND p.invoice_number LIKE ?";
        $params[] = "%$search%";
    }
    
    if ($supplier_filter) {
        $sql .= " AND p.supplier_id = ?";
        $params[] = $supplier_filter;
    }
    
    if ($status_filter) {
        $sql .= " AND p.payment_status = ?";
        $params[] = $status_filter;
    }
    
    $sql .= " ORDER BY p.created_at DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $purchases = $stmt->fetchAll();
}

// جلب الموردين والمنتجات
$stmt = $pdo->prepare("SELECT id, name FROM suppliers WHERE is_active = 1 ORDER BY name");
$stmt->execute();
$suppliers = $stmt->fetchAll();

$stmt = $pdo->prepare("SELECT id, name, cost_price FROM products WHERE is_active = 1 ORDER BY name");
$stmt->execute();
$products = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المشتريات - نظام إدارة المحل Z</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <?php if ($action === 'add'): ?>
                            إضافة فاتورة مشتريات
                        <?php elseif ($action === 'edit'): ?>
                            تعديل فاتورة المشتريات
                        <?php else: ?>
                            إدارة المشتريات
                        <?php endif; ?>
                    </h1>
                    
                    <?php if ($action === 'list'): ?>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="purchases.php?action=add" class="btn btn-sm btn-primary">
                                <i class="fas fa-plus"></i> فاتورة جديدة
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
                
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($action === 'add'): ?>
                    <!-- نموذج إضافة فاتورة مشتريات -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">إضافة فاتورة مشتريات جديدة</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" id="purchaseForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم الفاتورة *</label>
                                            <input type="text" class="form-control" name="invoice_number" required>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">المورد</label>
                                            <select class="form-select" name="supplier_id">
                                                <option value="">اختر المورد</option>
                                                <?php foreach ($suppliers as $supplier): ?>
                                                    <option value="<?= $supplier['id'] ?>"><?= htmlspecialchars($supplier['name']) ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">تاريخ الفاتورة *</label>
                                            <input type="date" class="form-control" name="purchase_date" value="<?= date('Y-m-d') ?>" required>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">المبلغ المدفوع</label>
                                            <input type="number" class="form-control" name="paid_amount" step="0.01" min="0" value="0">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="2"></textarea>
                                </div>
                                
                                <!-- المنتجات -->
                                <div class="card">
                                    <div class="card-header">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">المنتجات</h6>
                                            <button type="button" class="btn btn-sm btn-success" onclick="addProductRow()">
                                                <i class="fas fa-plus"></i> إضافة منتج
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table" id="productsTable">
                                                <thead>
                                                    <tr>
                                                        <th>المنتج</th>
                                                        <th>الكمية</th>
                                                        <th>سعر التكلفة</th>
                                                        <th>الإجمالي</th>
                                                        <th>إجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="productsTableBody">
                                                    <!-- سيتم إضافة الصفوف هنا -->
                                                </tbody>
                                                <tfoot>
                                                    <tr>
                                                        <th colspan="3">الإجمالي الكلي:</th>
                                                        <th id="grandTotal">0.00 ج.م</th>
                                                        <th></th>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                
                                <input type="hidden" name="items" id="itemsInput">
                                
                                <div class="mt-3">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> حفظ الفاتورة
                                    </button>
                                    <a href="purchases.php" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> إلغاء
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                <?php else: ?>
                    <!-- قائمة المشتريات -->
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h5 class="mb-0">فواتير المشتريات</h5>
                                </div>
                                <div class="col-md-6">
                                    <form method="get" class="d-flex gap-2">
                                        <input type="text" class="form-control form-control-sm" name="search" 
                                               placeholder="رقم الفاتورة..." value="<?= htmlspecialchars($_GET['search'] ?? '') ?>">
                                        <select class="form-select form-select-sm" name="supplier">
                                            <option value="">جميع الموردين</option>
                                            <?php foreach ($suppliers as $supplier): ?>
                                                <option value="<?= $supplier['id'] ?>" <?= ($_GET['supplier'] ?? '') == $supplier['id'] ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($supplier['name']) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <select class="form-select form-select-sm" name="status">
                                            <option value="">جميع الحالات</option>
                                            <option value="paid" <?= ($_GET['status'] ?? '') === 'paid' ? 'selected' : '' ?>>مدفوع</option>
                                            <option value="partial" <?= ($_GET['status'] ?? '') === 'partial' ? 'selected' : '' ?>>مدفوع جزئياً</option>
                                            <option value="unpaid" <?= ($_GET['status'] ?? '') === 'unpaid' ? 'selected' : '' ?>>غير مدفوع</option>
                                        </select>
                                        <button type="submit" class="btn btn-sm btn-primary">بحث</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>المورد</th>
                                            <th>التاريخ</th>
                                            <th>الإجمالي</th>
                                            <th>المدفوع</th>
                                            <th>المتبقي</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (empty($purchases)): ?>
                                            <tr>
                                                <td colspan="8" class="text-center text-muted py-4">
                                                    لا توجد فواتير مشتريات
                                                </td>
                                            </tr>
                                        <?php else: ?>
                                            <?php foreach ($purchases as $purchase): ?>
                                                <tr>
                                                    <td><strong><?= htmlspecialchars($purchase['invoice_number']) ?></strong></td>
                                                    <td><?= htmlspecialchars($purchase['supplier_name'] ?? 'غير محدد') ?></td>
                                                    <td><?= date('Y-m-d', strtotime($purchase['purchase_date'])) ?></td>
                                                    <td><?= number_format($purchase['total_amount'], 2) ?> ج.م</td>
                                                    <td><?= number_format($purchase['paid_amount'], 2) ?> ج.م</td>
                                                    <td><?= number_format($purchase['remaining_amount'], 2) ?> ج.م</td>
                                                    <td>
                                                        <?php
                                                        $status_classes = [
                                                            'paid' => 'bg-success',
                                                            'partial' => 'bg-warning',
                                                            'unpaid' => 'bg-danger'
                                                        ];
                                                        $status_labels = [
                                                            'paid' => 'مدفوع',
                                                            'partial' => 'مدفوع جزئياً',
                                                            'unpaid' => 'غير مدفوع'
                                                        ];
                                                        ?>
                                                        <span class="badge <?= $status_classes[$purchase['payment_status']] ?>">
                                                            <?= $status_labels[$purchase['payment_status']] ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="action-buttons">
                                                            <?php if ($purchase['remaining_amount'] > 0): ?>
                                                                <button type="button" class="btn btn-sm btn-success" 
                                                                        onclick="payPurchase(<?= $purchase['id'] ?>, '<?= htmlspecialchars($purchase['invoice_number']) ?>', <?= $purchase['remaining_amount'] ?>)" 
                                                                        title="دفع">
                                                                    <i class="fas fa-money-bill"></i>
                                                                </button>
                                                            <?php endif; ?>
                                                            <button type="button" class="btn btn-sm btn-danger" 
                                                                    onclick="deletePurchase(<?= $purchase['id'] ?>, '<?= htmlspecialchars($purchase['invoice_number']) ?>')" 
                                                                    title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>
    
    <!-- مودال دفع فاتورة -->
    <div class="modal fade" id="payPurchaseModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">دفع فاتورة مشتريات</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="post">
                    <input type="hidden" name="action" value="pay">
                    <input type="hidden" name="purchase_id" id="pay_purchase_id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">رقم الفاتورة</label>
                            <input type="text" class="form-control" id="pay_invoice_number" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">المبلغ المتبقي</label>
                            <input type="text" class="form-control" id="pay_remaining_amount" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">مبلغ الدفع</label>
                            <input type="number" class="form-control" name="payment_amount" step="0.01" min="0.01" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-success">تسجيل الدفعة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let products = <?= json_encode($products) ?>;
        let purchaseItems = [];
        
        function addProductRow() {
            const tbody = document.getElementById('productsTableBody');
            const rowIndex = tbody.children.length;
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <select class="form-select" onchange="updateProductPrice(${rowIndex})" required>
                        <option value="">اختر المنتج</option>
                        ${products.map(p => `<option value="${p.id}" data-cost="${p.cost_price}">${p.name}</option>`).join('')}
                    </select>
                </td>
                <td>
                    <input type="number" class="form-control" min="1" value="1" onchange="updateRowTotal(${rowIndex})" required>
                </td>
                <td>
                    <input type="number" class="form-control" step="0.01" min="0.01" onchange="updateRowTotal(${rowIndex})" required>
                </td>
                <td class="row-total">0.00 ج.م</td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger" onclick="removeProductRow(${rowIndex})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            
            tbody.appendChild(row);
        }
        
        function updateProductPrice(rowIndex) {
            const row = document.getElementById('productsTableBody').children[rowIndex];
            const select = row.querySelector('select');
            const priceInput = row.querySelector('input[type="number"]:nth-of-type(2)');
            
            const selectedOption = select.selectedOptions[0];
            if (selectedOption && selectedOption.dataset.cost) {
                priceInput.value = selectedOption.dataset.cost;
                updateRowTotal(rowIndex);
            }
        }
        
        function updateRowTotal(rowIndex) {
            const row = document.getElementById('productsTableBody').children[rowIndex];
            const quantity = parseFloat(row.querySelector('input[type="number"]:nth-of-type(1)').value) || 0;
            const price = parseFloat(row.querySelector('input[type="number"]:nth-of-type(2)').value) || 0;
            const total = quantity * price;
            
            row.querySelector('.row-total').textContent = total.toFixed(2) + ' ج.م';
            updateGrandTotal();
        }
        
        function removeProductRow(rowIndex) {
            const tbody = document.getElementById('productsTableBody');
            tbody.removeChild(tbody.children[rowIndex]);
            updateGrandTotal();
        }
        
        function updateGrandTotal() {
            const rows = document.getElementById('productsTableBody').children;
            let grandTotal = 0;
            
            for (let i = 0; i < rows.length; i++) {
                const quantity = parseFloat(rows[i].querySelector('input[type="number"]:nth-of-type(1)').value) || 0;
                const price = parseFloat(rows[i].querySelector('input[type="number"]:nth-of-type(2)').value) || 0;
                grandTotal += quantity * price;
            }
            
            document.getElementById('grandTotal').textContent = grandTotal.toFixed(2) + ' ج.م';
        }
        
        function payPurchase(purchaseId, invoiceNumber, remainingAmount) {
            document.getElementById('pay_purchase_id').value = purchaseId;
            document.getElementById('pay_invoice_number').value = invoiceNumber;
            document.getElementById('pay_remaining_amount').value = remainingAmount.toFixed(2) + ' ج.م';
            
            new bootstrap.Modal(document.getElementById('payPurchaseModal')).show();
        }
        
        function deletePurchase(purchaseId, invoiceNumber) {
            if (confirm(`هل أنت متأكد من حذف فاتورة "${invoiceNumber}"؟ سيتم إرجاع المخزون.`)) {
                const form = document.createElement('form');
                form.method = 'post';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="purchase_id" value="${purchaseId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        // معالجة إرسال النموذج
        document.getElementById('purchaseForm')?.addEventListener('submit', function(e) {
            const rows = document.getElementById('productsTableBody').children;
            const items = [];
            
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const productId = row.querySelector('select').value;
                const quantity = parseFloat(row.querySelector('input[type="number"]:nth-of-type(1)').value);
                const costPrice = parseFloat(row.querySelector('input[type="number"]:nth-of-type(2)').value);
                
                if (productId && quantity > 0 && costPrice > 0) {
                    items.push({
                        product_id: productId,
                        quantity: quantity,
                        cost_price: costPrice
                    });
                }
            }
            
            if (items.length === 0) {
                e.preventDefault();
                alert('يجب إضافة منتج واحد على الأقل');
                return;
            }
            
            document.getElementById('itemsInput').value = JSON.stringify(items);
        });
        
        // إضافة صف أولي
        if (document.getElementById('productsTableBody')) {
            addProductRow();
        }
    </script>
</body>
</html>
