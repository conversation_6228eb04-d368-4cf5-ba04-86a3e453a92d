<?php
/**
 * نظام إدارة المحل Z - إدارة الموردين
 */

require_once 'config.php';
require_login();

$action = $_GET['action'] ?? 'list';
$supplier_id = $_GET['id'] ?? null;
$message = '';
$error = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if ($action === 'add' || $action === 'edit') {
            $name = trim($_POST['name']);
            $contact_person = trim($_POST['contact_person']);
            $phone = trim($_POST['phone']);
            $email = trim($_POST['email']);
            $address = trim($_POST['address']);
            
            // التحقق من البيانات المطلوبة
            if (empty($name)) {
                throw new Exception('يرجى إدخال اسم المورد');
            }
            
            if ($action === 'add') {
                // إضافة مورد جديد
                $stmt = $pdo->prepare("
                    INSERT INTO suppliers (name, contact_person, phone, email, address) 
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([$name, $contact_person, $phone, $email, $address]);
                
                $message = 'تم إضافة المورد بنجاح';
                
            } else {
                // تعديل مورد موجود
                $stmt = $pdo->prepare("
                    UPDATE suppliers SET name = ?, contact_person = ?, phone = ?, email = ?, address = ? 
                    WHERE id = ?
                ");
                $stmt->execute([$name, $contact_person, $phone, $email, $address, $supplier_id]);
                
                $message = 'تم تحديث بيانات المورد بنجاح';
            }
            
        } elseif ($action === 'delete') {
            $supplier_id = $_POST['supplier_id'];
            
            // التحقق من عدم وجود مشتريات للمورد
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM purchases WHERE supplier_id = ?");
            $stmt->execute([$supplier_id]);
            $has_purchases = $stmt->fetchColumn();
            
            if ($has_purchases > 0) {
                // إلغاء تفعيل المورد بدلاً من حذفه
                $stmt = $pdo->prepare("UPDATE suppliers SET is_active = 0 WHERE id = ?");
                $stmt->execute([$supplier_id]);
                $message = 'تم إلغاء تفعيل المورد';
            } else {
                // حذف المورد نهائياً
                $stmt = $pdo->prepare("DELETE FROM suppliers WHERE id = ?");
                $stmt->execute([$supplier_id]);
                $message = 'تم حذف المورد';
            }
            
        } elseif ($action === 'pay_balance') {
            $supplier_id = $_POST['supplier_id'];
            $payment_amount = floatval($_POST['payment_amount']);
            $notes = trim($_POST['notes']);
            
            if ($payment_amount <= 0) {
                throw new Exception('يرجى إدخال مبلغ صحيح');
            }
            
            // الحصول على رصيد المورد
            $stmt = $pdo->prepare("SELECT balance FROM suppliers WHERE id = ?");
            $stmt->execute([$supplier_id]);
            $current_balance = $stmt->fetchColumn();
            
            if ($current_balance !== false) {
                if ($payment_amount > $current_balance) {
                    throw new Exception('المبلغ المدفوع أكبر من الرصيد المستحق');
                }
                
                $new_balance = $current_balance - $payment_amount;
                
                // تحديث رصيد المورد
                $stmt = $pdo->prepare("UPDATE suppliers SET balance = ? WHERE id = ?");
                $stmt->execute([$new_balance, $supplier_id]);
                
                // تسجيل الدفعة في الخزنة
                $stmt = $pdo->prepare("SELECT balance_after FROM cash_register ORDER BY id DESC LIMIT 1");
                $stmt->execute();
                $cash_balance = $stmt->fetchColumn() ?: 0;
                
                $stmt = $pdo->prepare("
                    INSERT INTO cash_register (transaction_type, amount, balance_before, balance_after, 
                                             reference_type, description, created_by) 
                    VALUES ('expense', ?, ?, ?, 'manual', ?, ?)
                ");
                $stmt->execute([
                    $payment_amount, 
                    $cash_balance, 
                    $cash_balance - $payment_amount,
                    "دفع للمورد - $notes",
                    $_SESSION['user_id']
                ]);
                
                $message = 'تم تسجيل الدفعة بنجاح';
            }
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// جلب البيانات للعرض
if ($action === 'edit' && $supplier_id) {
    $stmt = $pdo->prepare("SELECT * FROM suppliers WHERE id = ?");
    $stmt->execute([$supplier_id]);
    $supplier = $stmt->fetch();
    
    if (!$supplier) {
        $error = 'المورد غير موجود';
        $action = 'list';
    }
}

// جلب الموردين للعرض
if ($action === 'list') {
    $search = $_GET['search'] ?? '';
    $filter = $_GET['filter'] ?? '';
    
    $sql = "SELECT * FROM suppliers WHERE is_active = 1";
    $params = [];
    
    if ($search) {
        $sql .= " AND (name LIKE ? OR contact_person LIKE ? OR phone LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if ($filter === 'balance') {
        $sql .= " AND balance > 0";
    }
    
    $sql .= " ORDER BY name";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $suppliers = $stmt->fetchAll();
}

// جلب إحصائيات الموردين
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM suppliers WHERE is_active = 1");
    $stmt->execute();
    $total_suppliers = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COALESCE(SUM(balance), 0) FROM suppliers WHERE balance > 0");
    $stmt->execute();
    $total_balance = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM suppliers WHERE balance > 0");
    $stmt->execute();
    $suppliers_with_balance = $stmt->fetchColumn();
    
} catch (PDOException $e) {
    $total_suppliers = $total_balance = $suppliers_with_balance = 0;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموردين - نظام إدارة المحل Z</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <?php if ($action === 'add'): ?>
                            إضافة مورد جديد
                        <?php elseif ($action === 'edit'): ?>
                            تعديل بيانات المورد
                        <?php else: ?>
                            إدارة الموردين
                        <?php endif; ?>
                    </h1>
                    
                    <?php if ($action === 'list'): ?>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="suppliers.php?action=add" class="btn btn-sm btn-primary">
                                <i class="fas fa-plus"></i> إضافة مورد
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
                
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($action === 'list'): ?>
                    <!-- إحصائيات الموردين -->
                    <div class="row mb-4">
                        <div class="col-xl-4 col-md-6 mb-4">
                            <div class="card border-right-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                إجمالي الموردين
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($total_suppliers) ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-truck fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-4 col-md-6 mb-4">
                            <div class="card border-right-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                إجمالي المستحقات
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($total_balance, 2) ?> ج.م
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-4 col-md-6 mb-4">
                            <div class="card border-right-info shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                موردين لهم مستحقات
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($suppliers_with_balance) ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-user-clock fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if ($action === 'add' || $action === 'edit'): ?>
                    <!-- نموذج إضافة/تعديل المورد -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <?= $action === 'add' ? 'إضافة مورد جديد' : 'تعديل بيانات المورد' ?>
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="post" class="needs-validation" novalidate>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">اسم المورد *</label>
                                            <input type="text" class="form-control" name="name" 
                                                   value="<?= htmlspecialchars($supplier['name'] ?? '') ?>" required>
                                            <div class="invalid-feedback">يرجى إدخال اسم المورد</div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الشخص المسؤول</label>
                                            <input type="text" class="form-control" name="contact_person" 
                                                   value="<?= htmlspecialchars($supplier['contact_person'] ?? '') ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم الهاتف</label>
                                            <input type="tel" class="form-control" name="phone" 
                                                   value="<?= htmlspecialchars($supplier['phone'] ?? '') ?>">
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">البريد الإلكتروني</label>
                                            <input type="email" class="form-control" name="email" 
                                                   value="<?= htmlspecialchars($supplier['email'] ?? '') ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="address" rows="3"><?= htmlspecialchars($supplier['address'] ?? '') ?></textarea>
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i>
                                        <?= $action === 'add' ? 'إضافة المورد' : 'حفظ التغييرات' ?>
                                    </button>
                                    <a href="suppliers.php" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> إلغاء
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                <?php else: ?>
                    <!-- قائمة الموردين -->
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h5 class="mb-0">قائمة الموردين</h5>
                                </div>
                                <div class="col-md-6">
                                    <form method="get" class="d-flex gap-2">
                                        <input type="text" class="form-control form-control-sm" name="search" 
                                               placeholder="البحث..." value="<?= htmlspecialchars($_GET['search'] ?? '') ?>">
                                        <select class="form-select form-select-sm" name="filter">
                                            <option value="">جميع الموردين</option>
                                            <option value="balance" <?= ($_GET['filter'] ?? '') === 'balance' ? 'selected' : '' ?>>لهم مستحقات</option>
                                        </select>
                                        <button type="submit" class="btn btn-sm btn-primary">بحث</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>اسم المورد</th>
                                            <th>الشخص المسؤول</th>
                                            <th>رقم الهاتف</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>المستحقات</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (empty($suppliers)): ?>
                                            <tr>
                                                <td colspan="6" class="text-center text-muted py-4">
                                                    لا توجد موردين
                                                </td>
                                            </tr>
                                        <?php else: ?>
                                            <?php foreach ($suppliers as $supplier): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?= htmlspecialchars($supplier['name']) ?></strong>
                                                        <?php if ($supplier['address']): ?>
                                                            <br><small class="text-muted"><?= htmlspecialchars(substr($supplier['address'], 0, 50)) ?>...</small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?= htmlspecialchars($supplier['contact_person']) ?></td>
                                                    <td><?= htmlspecialchars($supplier['phone']) ?></td>
                                                    <td><?= htmlspecialchars($supplier['email']) ?></td>
                                                    <td>
                                                        <span class="badge <?= $supplier['balance'] > 0 ? 'bg-warning' : 'bg-secondary' ?>">
                                                            <?= number_format($supplier['balance'], 2) ?> ج.م
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="action-buttons">
                                                            <a href="suppliers.php?action=edit&id=<?= $supplier['id'] ?>" 
                                                               class="btn btn-sm btn-primary" title="تعديل">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <?php if ($supplier['balance'] > 0): ?>
                                                                <button type="button" class="btn btn-sm btn-success" 
                                                                        onclick="payBalance(<?= $supplier['id'] ?>, '<?= htmlspecialchars($supplier['name']) ?>', <?= $supplier['balance'] ?>)" 
                                                                        title="دفع مستحقات">
                                                                    <i class="fas fa-money-bill"></i>
                                                                </button>
                                                            <?php endif; ?>
                                                            <button type="button" class="btn btn-sm btn-danger" 
                                                                    onclick="deleteSupplier(<?= $supplier['id'] ?>, '<?= htmlspecialchars($supplier['name']) ?>')" 
                                                                    title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>
    
    <!-- مودال دفع المستحقات -->
    <div class="modal fade" id="payBalanceModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">دفع مستحقات المورد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="post">
                    <input type="hidden" name="action" value="pay_balance">
                    <input type="hidden" name="supplier_id" id="pay_supplier_id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">المورد</label>
                            <input type="text" class="form-control" id="pay_supplier_name" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">المستحقات الحالية</label>
                            <input type="text" class="form-control" id="pay_current_balance" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">مبلغ الدفع</label>
                            <input type="number" class="form-control" name="payment_amount" step="0.01" min="0.01" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-success">تسجيل الدفعة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function payBalance(supplierId, supplierName, currentBalance) {
            document.getElementById('pay_supplier_id').value = supplierId;
            document.getElementById('pay_supplier_name').value = supplierName;
            document.getElementById('pay_current_balance').value = currentBalance.toFixed(2) + ' ج.م';
            
            new bootstrap.Modal(document.getElementById('payBalanceModal')).show();
        }
        
        function deleteSupplier(supplierId, supplierName) {
            if (confirm(`هل أنت متأكد من حذف المورد "${supplierName}"؟`)) {
                const form = document.createElement('form');
                form.method = 'post';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="supplier_id" value="${supplierId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        // تفعيل التحقق من صحة النماذج
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
</body>
</html>
