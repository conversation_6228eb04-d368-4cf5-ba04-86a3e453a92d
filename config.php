<?php
// نظام إدارة المحل Z - إعدادات قاعدة البيانات
session_start();

// إعدادات قاعدة البيانات
$db_host = 'localhost';
$db_name = 'store_z';
$db_user = 'root';
$db_pass = '';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    // إذا لم تكن قاعدة البيانات موجودة، حاول إنشاؤها
    try {
        $pdo = new PDO("mysql:host=$db_host;charset=utf8mb4", $db_user, $db_pass);
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    } catch(PDOException $e2) {
        die("خطأ في الاتصال بقاعدة البيانات: " . $e2->getMessage());
    }
}

// دالة التحقق من تسجيل الدخول
function require_login() {
    if (!isset($_SESSION['user_id'])) {
        header('Location: login.php');
        exit;
    }
}

// دالة إنشاء رقم فاتورة
function generate_invoice_number() {
    return 'INV-' . date('Ymd') . '-' . rand(1000, 9999);
}

// دالة إنشاء باركود
function generate_barcode() {
    return date('Ymd') . rand(100000, 999999);
}
?>
