<?php
/**
 * نظام إدارة المحل Z - إدارة المبيعات
 */

require_once 'config.php';
require_login();

$action = $_GET['action'] ?? 'list';
$sale_id = $_GET['id'] ?? null;
$message = '';
$error = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if ($action === 'delete') {
            $sale_id = $_POST['sale_id'];
            
            $pdo->beginTransaction();
            
            // الحصول على تفاصيل الفاتورة
            $stmt = $pdo->prepare("SELECT * FROM sales WHERE id = ?");
            $stmt->execute([$sale_id]);
            $sale = $stmt->fetch();
            
            if ($sale) {
                // الحصول على تفاصيل المنتجات
                $stmt = $pdo->prepare("SELECT * FROM sale_items WHERE sale_id = ?");
                $stmt->execute([$sale_id]);
                $items = $stmt->fetchAll();
                
                // إرجاع المخزون
                foreach ($items as $item) {
                    $stmt = $pdo->prepare("
                        UPDATE products 
                        SET stock_quantity = stock_quantity + ? 
                        WHERE id = ?
                    ");
                    $stmt->execute([$item['quantity'], $item['product_id']]);
                    
                    // تسجيل حركة المخزون
                    $stmt = $pdo->prepare("
                        INSERT INTO stock_movements (product_id, movement_type, quantity, reference_type, reference_id, notes, created_by) 
                        VALUES (?, 'in', ?, 'adjustment', ?, 'حذف فاتورة بيع', ?)
                    ");
                    $stmt->execute([$item['product_id'], $item['quantity'], $sale_id, $_SESSION['user_id']]);
                }
                
                // تحديث بيانات العميل
                if ($sale['customer_id']) {
                    $stmt = $pdo->prepare("SELECT wallet_balance, debt_balance, total_purchases FROM customers WHERE id = ?");
                    $stmt->execute([$sale['customer_id']]);
                    $customer_data = $stmt->fetch();
                    
                    if ($customer_data) {
                        $new_wallet = $customer_data['wallet_balance'] + $sale['wallet_used'] - $sale['change_amount'];
                        $new_debt = $customer_data['debt_balance'] - $sale['remaining_amount'];
                        $new_total = $customer_data['total_purchases'] - $sale['total_amount'];
                        
                        $stmt = $pdo->prepare("
                            UPDATE customers 
                            SET wallet_balance = ?, debt_balance = ?, total_purchases = ? 
                            WHERE id = ?
                        ");
                        $stmt->execute([$new_wallet, $new_debt, $new_total, $sale['customer_id']]);
                    }
                }
                
                // حذف التفاصيل والفاتورة
                $stmt = $pdo->prepare("DELETE FROM sale_items WHERE sale_id = ?");
                $stmt->execute([$sale_id]);
                
                $stmt = $pdo->prepare("DELETE FROM sales WHERE id = ?");
                $stmt->execute([$sale_id]);
                
                $message = 'تم حذف فاتورة البيع';
            }
            
            $pdo->commit();
            
        } elseif ($action === 'pay_debt') {
            $sale_id = $_POST['sale_id'];
            $payment_amount = floatval($_POST['payment_amount']);
            $use_wallet = isset($_POST['use_wallet']);
            
            if ($payment_amount <= 0) {
                throw new Exception('يرجى إدخال مبلغ صحيح');
            }
            
            // الحصول على بيانات الفاتورة والعميل
            $stmt = $pdo->prepare("
                SELECT s.*, c.wallet_balance 
                FROM sales s 
                LEFT JOIN customers c ON s.customer_id = c.id 
                WHERE s.id = ?
            ");
            $stmt->execute([$sale_id]);
            $sale_data = $stmt->fetch();
            
            if ($sale_data && $payment_amount <= $sale_data['remaining_amount']) {
                $wallet_used = 0;
                $cash_payment = $payment_amount;
                
                // استخدام الرصيد المحفوظ إذا طُلب ذلك
                if ($use_wallet && $sale_data['wallet_balance'] > 0) {
                    $wallet_used = min($sale_data['wallet_balance'], $payment_amount);
                    $cash_payment = $payment_amount - $wallet_used;
                }
                
                // تحديث الفاتورة
                $new_paid = $sale_data['paid_amount'] + $cash_payment;
                $new_wallet_used = $sale_data['wallet_used'] + $wallet_used;
                $new_remaining = $sale_data['remaining_amount'] - $payment_amount;
                $new_status = $new_remaining > 0 ? 'partial' : 'paid';
                
                $stmt = $pdo->prepare("
                    UPDATE sales 
                    SET paid_amount = ?, wallet_used = ?, remaining_amount = ?, payment_status = ? 
                    WHERE id = ?
                ");
                $stmt->execute([$new_paid, $new_wallet_used, $new_remaining, $new_status, $sale_id]);
                
                // تحديث بيانات العميل
                if ($sale_data['customer_id']) {
                    $new_wallet_balance = $sale_data['wallet_balance'] - $wallet_used;
                    $new_debt_balance = $sale_data['debt_balance'] - $payment_amount;
                    
                    $stmt = $pdo->prepare("
                        UPDATE customers 
                        SET wallet_balance = ?, debt_balance = ? 
                        WHERE id = ?
                    ");
                    $stmt->execute([$new_wallet_balance, $new_debt_balance, $sale_data['customer_id']]);
                }
                
                // تحديث الخزنة (إذا كان هناك دفع نقدي)
                if ($cash_payment > 0) {
                    $stmt = $pdo->prepare("SELECT balance_after FROM cash_register ORDER BY id DESC LIMIT 1");
                    $stmt->execute();
                    $current_balance = $stmt->fetchColumn() ?: 0;
                    
                    $stmt = $pdo->prepare("
                        INSERT INTO cash_register (transaction_type, amount, balance_before, balance_after, 
                                                 reference_type, reference_id, description, created_by) 
                        VALUES ('income', ?, ?, ?, 'sale', ?, ?, ?)
                    ");
                    $stmt->execute([
                        $cash_payment, $current_balance, $current_balance + $cash_payment,
                        $sale_id, "سداد دين - فاتورة رقم: {$sale_data['invoice_number']}", $_SESSION['user_id']
                    ]);
                }
                
                $message = 'تم تسجيل الدفعة بنجاح';
            } else {
                throw new Exception('مبلغ الدفع غير صحيح');
            }
        }
        
    } catch (Exception $e) {
        if (isset($pdo)) {
            $pdo->rollBack();
        }
        $error = $e->getMessage();
    }
}

// جلب البيانات للعرض
if ($action === 'view' && $sale_id) {
    $stmt = $pdo->prepare("
        SELECT s.*, c.name as customer_name, c.phone as customer_phone, c.whatsapp as customer_whatsapp,
               u.full_name as cashier_name
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
        LEFT JOIN users u ON s.created_by = u.id
        WHERE s.id = ?
    ");
    $stmt->execute([$sale_id]);
    $sale = $stmt->fetch();
    
    if (!$sale) {
        $error = 'فاتورة البيع غير موجودة';
        $action = 'list';
    } else {
        // جلب تفاصيل الفاتورة
        $stmt = $pdo->prepare("
            SELECT si.*, p.name as product_name, p.unit
            FROM sale_items si
            JOIN products p ON si.product_id = p.id
            WHERE si.sale_id = ?
            ORDER BY si.id
        ");
        $stmt->execute([$sale_id]);
        $sale_items = $stmt->fetchAll();
    }
}

// جلب المبيعات للعرض
if ($action === 'list') {
    $search = $_GET['search'] ?? '';
    $customer_filter = $_GET['customer'] ?? '';
    $status_filter = $_GET['status'] ?? '';
    $date_from = $_GET['date_from'] ?? '';
    $date_to = $_GET['date_to'] ?? '';
    
    $sql = "
        SELECT s.*, c.name as customer_name 
        FROM sales s 
        LEFT JOIN customers c ON s.customer_id = c.id 
        WHERE 1=1
    ";
    $params = [];
    
    if ($search) {
        $sql .= " AND s.invoice_number LIKE ?";
        $params[] = "%$search%";
    }
    
    if ($customer_filter) {
        $sql .= " AND s.customer_id = ?";
        $params[] = $customer_filter;
    }
    
    if ($status_filter) {
        $sql .= " AND s.payment_status = ?";
        $params[] = $status_filter;
    }
    
    if ($date_from) {
        $sql .= " AND s.sale_date >= ?";
        $params[] = $date_from;
    }
    
    if ($date_to) {
        $sql .= " AND s.sale_date <= ?";
        $params[] = $date_to;
    }
    
    $sql .= " ORDER BY s.created_at DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $sales = $stmt->fetchAll();
}

// جلب العملاء
$stmt = $pdo->prepare("SELECT id, name FROM customers WHERE is_active = 1 ORDER BY name");
$stmt->execute();
$customers = $stmt->fetchAll();

// جلب إحصائيات المبيعات
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM sales");
    $stmt->execute();
    $total_sales = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COALESCE(SUM(total_amount), 0) FROM sales WHERE DATE(sale_date) = CURDATE()");
    $stmt->execute();
    $today_sales = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COALESCE(SUM(remaining_amount), 0) FROM sales WHERE remaining_amount > 0");
    $stmt->execute();
    $total_debts = $stmt->fetchColumn();
    
} catch (PDOException $e) {
    $total_sales = $today_sales = $total_debts = 0;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المبيعات - نظام إدارة المحل Z</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <?php if ($action === 'view'): ?>
                            تفاصيل فاتورة البيع
                        <?php else: ?>
                            إدارة المبيعات
                        <?php endif; ?>
                    </h1>
                    
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="pos.php" class="btn btn-sm btn-primary">
                                <i class="fas fa-plus"></i> بيع جديد
                            </a>
                            <?php if ($action === 'view'): ?>
                                <a href="print_invoice.php?id=<?= $sale_id ?>" class="btn btn-sm btn-secondary" target="_blank">
                                    <i class="fas fa-print"></i> طباعة
                                </a>
                                <a href="sales.php" class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-arrow-right"></i> العودة
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($action === 'view' && isset($sale)): ?>
                    <!-- تفاصيل الفاتورة -->
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">تفاصيل الفاتورة رقم: <?= htmlspecialchars($sale['invoice_number']) ?></h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <strong>التاريخ:</strong> <?= date('Y-m-d', strtotime($sale['sale_date'])) ?><br>
                                            <strong>الوقت:</strong> <?= date('H:i', strtotime($sale['created_at'])) ?><br>
                                            <strong>الكاشير:</strong> <?= htmlspecialchars($sale['cashier_name']) ?>
                                        </div>
                                        <div class="col-md-6">
                                            <strong>العميل:</strong> <?= htmlspecialchars($sale['customer_name'] ?? 'عميل نقدي') ?><br>
                                            <?php if ($sale['customer_phone']): ?>
                                                <strong>الهاتف:</strong> <?= htmlspecialchars($sale['customer_phone']) ?><br>
                                            <?php endif; ?>
                                            <?php if ($sale['customer_whatsapp']): ?>
                                                <strong>واتساب:</strong> 
                                                <a href="https://wa.me/<?= htmlspecialchars($sale['customer_whatsapp']) ?>" target="_blank" class="text-success">
                                                    <?= htmlspecialchars($sale['customer_whatsapp']) ?>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>#</th>
                                                    <th>المنتج</th>
                                                    <th>الكمية</th>
                                                    <th>الوحدة</th>
                                                    <th>السعر</th>
                                                    <th>الإجمالي</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($sale_items as $index => $item): ?>
                                                    <tr>
                                                        <td><?= $index + 1 ?></td>
                                                        <td><?= htmlspecialchars($item['product_name']) ?></td>
                                                        <td><?= $item['quantity'] ?></td>
                                                        <td><?= htmlspecialchars($item['unit']) ?></td>
                                                        <td><?= number_format($item['unit_price'], 2) ?> ج.م</td>
                                                        <td><?= number_format($item['total_price'], 2) ?> ج.م</td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <th colspan="5">المجموع الفرعي:</th>
                                                    <th><?= number_format(array_sum(array_column($sale_items, 'total_price')), 2) ?> ج.م</th>
                                                </tr>
                                                <?php if ($sale['discount_amount'] > 0): ?>
                                                    <tr>
                                                        <th colspan="5">الخصم:</th>
                                                        <th><?= number_format($sale['discount_amount'], 2) ?> ج.م</th>
                                                    </tr>
                                                <?php endif; ?>
                                                <tr class="table-primary">
                                                    <th colspan="5">الإجمالي النهائي:</th>
                                                    <th><?= number_format($sale['total_amount'], 2) ?> ج.م</th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">تفاصيل الدفع</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-2">
                                        <strong>طريقة الدفع:</strong>
                                        <?php
                                        $payment_types = [
                                            'cash' => 'نقدي',
                                            'credit' => 'آجل',
                                            'wallet' => 'رصيد محفوظ',
                                            'mixed' => 'مختلط'
                                        ];
                                        echo $payment_types[$sale['payment_type']] ?? $sale['payment_type'];
                                        ?>
                                    </div>
                                    
                                    <?php if ($sale['paid_amount'] > 0): ?>
                                        <div class="mb-2">
                                            <strong>المبلغ المدفوع نقداً:</strong> <?= number_format($sale['paid_amount'], 2) ?> ج.م
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($sale['wallet_used'] > 0): ?>
                                        <div class="mb-2">
                                            <strong>المستخدم من الرصيد:</strong> <?= number_format($sale['wallet_used'], 2) ?> ج.م
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($sale['change_amount'] > 0): ?>
                                        <div class="mb-2 text-success">
                                            <strong>الفكة:</strong> <?= number_format($sale['change_amount'], 2) ?> ج.م
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($sale['remaining_amount'] > 0): ?>
                                        <div class="mb-2 text-warning">
                                            <strong>المبلغ المتبقي (دين):</strong> <?= number_format($sale['remaining_amount'], 2) ?> ج.م
                                        </div>
                                        
                                        <?php if ($sale['customer_id']): ?>
                                            <button type="button" class="btn btn-sm btn-success w-100" 
                                                    onclick="payDebt(<?= $sale['id'] ?>, '<?= htmlspecialchars($sale['invoice_number']) ?>', <?= $sale['remaining_amount'] ?>)">
                                                <i class="fas fa-money-bill"></i> سداد الدين
                                            </button>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <div class="alert alert-success">
                                            <i class="fas fa-check-circle"></i> تم السداد بالكامل
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="mt-3">
                                        <strong>حالة الدفع:</strong>
                                        <?php
                                        $status_classes = [
                                            'paid' => 'bg-success',
                                            'partial' => 'bg-warning',
                                            'unpaid' => 'bg-danger'
                                        ];
                                        $status_labels = [
                                            'paid' => 'مدفوع',
                                            'partial' => 'مدفوع جزئياً',
                                            'unpaid' => 'غير مدفوع'
                                        ];
                                        ?>
                                        <span class="badge <?= $status_classes[$sale['payment_status']] ?>">
                                            <?= $status_labels[$sale['payment_status']] ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                <?php else: ?>
                    <!-- إحصائيات المبيعات -->
                    <div class="row mb-4">
                        <div class="col-xl-4 col-md-6 mb-4">
                            <div class="card border-right-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                إجمالي المبيعات
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($total_sales) ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-shopping-bag fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-4 col-md-6 mb-4">
                            <div class="card border-right-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                مبيعات اليوم
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($today_sales, 2) ?> ج.م
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-cash-register fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-4 col-md-6 mb-4">
                            <div class="card border-right-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                إجمالي الديون
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($total_debts, 2) ?> ج.م
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- قائمة المبيعات -->
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h5 class="mb-0">فواتير المبيعات</h5>
                                </div>
                                <div class="col-md-6">
                                    <form method="get" class="d-flex gap-2">
                                        <input type="text" class="form-control form-control-sm" name="search" 
                                               placeholder="رقم الفاتورة..." value="<?= htmlspecialchars($_GET['search'] ?? '') ?>">
                                        <select class="form-select form-select-sm" name="customer">
                                            <option value="">جميع العملاء</option>
                                            <?php foreach ($customers as $customer): ?>
                                                <option value="<?= $customer['id'] ?>" <?= ($_GET['customer'] ?? '') == $customer['id'] ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($customer['name']) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <select class="form-select form-select-sm" name="status">
                                            <option value="">جميع الحالات</option>
                                            <option value="paid" <?= ($_GET['status'] ?? '') === 'paid' ? 'selected' : '' ?>>مدفوع</option>
                                            <option value="partial" <?= ($_GET['status'] ?? '') === 'partial' ? 'selected' : '' ?>>مدفوع جزئياً</option>
                                            <option value="unpaid" <?= ($_GET['status'] ?? '') === 'unpaid' ? 'selected' : '' ?>>غير مدفوع</option>
                                        </select>
                                        <button type="submit" class="btn btn-sm btn-primary">بحث</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>العميل</th>
                                            <th>التاريخ</th>
                                            <th>الإجمالي</th>
                                            <th>المدفوع</th>
                                            <th>المتبقي</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (empty($sales)): ?>
                                            <tr>
                                                <td colspan="8" class="text-center text-muted py-4">
                                                    لا توجد فواتير مبيعات
                                                </td>
                                            </tr>
                                        <?php else: ?>
                                            <?php foreach ($sales as $sale): ?>
                                                <tr>
                                                    <td>
                                                        <a href="sales.php?action=view&id=<?= $sale['id'] ?>" class="text-decoration-none">
                                                            <strong><?= htmlspecialchars($sale['invoice_number']) ?></strong>
                                                        </a>
                                                    </td>
                                                    <td><?= htmlspecialchars($sale['customer_name'] ?? 'عميل نقدي') ?></td>
                                                    <td><?= date('Y-m-d', strtotime($sale['sale_date'])) ?></td>
                                                    <td><?= number_format($sale['total_amount'], 2) ?> ج.م</td>
                                                    <td><?= number_format($sale['paid_amount'] + $sale['wallet_used'], 2) ?> ج.م</td>
                                                    <td><?= number_format($sale['remaining_amount'], 2) ?> ج.م</td>
                                                    <td>
                                                        <?php
                                                        $status_classes = [
                                                            'paid' => 'bg-success',
                                                            'partial' => 'bg-warning',
                                                            'unpaid' => 'bg-danger'
                                                        ];
                                                        $status_labels = [
                                                            'paid' => 'مدفوع',
                                                            'partial' => 'مدفوع جزئياً',
                                                            'unpaid' => 'غير مدفوع'
                                                        ];
                                                        ?>
                                                        <span class="badge <?= $status_classes[$sale['payment_status']] ?>">
                                                            <?= $status_labels[$sale['payment_status']] ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="action-buttons">
                                                            <a href="sales.php?action=view&id=<?= $sale['id'] ?>" 
                                                               class="btn btn-sm btn-primary" title="عرض">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <a href="print_invoice.php?id=<?= $sale['id'] ?>" 
                                                               class="btn btn-sm btn-secondary" title="طباعة" target="_blank">
                                                                <i class="fas fa-print"></i>
                                                            </a>
                                                            <?php if ($sale['remaining_amount'] > 0 && $sale['customer_id']): ?>
                                                                <button type="button" class="btn btn-sm btn-success" 
                                                                        onclick="payDebt(<?= $sale['id'] ?>, '<?= htmlspecialchars($sale['invoice_number']) ?>', <?= $sale['remaining_amount'] ?>)" 
                                                                        title="سداد دين">
                                                                    <i class="fas fa-money-bill"></i>
                                                                </button>
                                                            <?php endif; ?>
                                                            <button type="button" class="btn btn-sm btn-danger" 
                                                                    onclick="deleteSale(<?= $sale['id'] ?>, '<?= htmlspecialchars($sale['invoice_number']) ?>')" 
                                                                    title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>
    
    <!-- مودال سداد الدين -->
    <div class="modal fade" id="payDebtModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">سداد دين فاتورة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="post">
                    <input type="hidden" name="action" value="pay_debt">
                    <input type="hidden" name="sale_id" id="debt_sale_id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">رقم الفاتورة</label>
                            <input type="text" class="form-control" id="debt_invoice_number" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">المبلغ المتبقي</label>
                            <input type="text" class="form-control" id="debt_remaining_amount" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">مبلغ السداد</label>
                            <input type="number" class="form-control" name="payment_amount" step="0.01" min="0.01" required>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="use_wallet" id="use_wallet">
                                <label class="form-check-label" for="use_wallet">
                                    استخدام الرصيد المحفوظ للعميل
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-success">تسجيل السداد</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function payDebt(saleId, invoiceNumber, remainingAmount) {
            document.getElementById('debt_sale_id').value = saleId;
            document.getElementById('debt_invoice_number').value = invoiceNumber;
            document.getElementById('debt_remaining_amount').value = remainingAmount.toFixed(2) + ' ج.م';
            
            new bootstrap.Modal(document.getElementById('payDebtModal')).show();
        }
        
        function deleteSale(saleId, invoiceNumber) {
            if (confirm(`هل أنت متأكد من حذف فاتورة "${invoiceNumber}"؟ سيتم إرجاع المخزون وتحديث بيانات العميل.`)) {
                const form = document.createElement('form');
                form.method = 'post';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="sale_id" value="${saleId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
