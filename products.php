<?php
/**
 * نظام إدارة المحل Z - إدارة المنتجات
 */

require_once 'config.php';
require_login();

$action = $_GET['action'] ?? 'list';
$product_id = $_GET['id'] ?? null;
$message = '';
$error = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if ($action === 'add' || $action === 'edit') {
            $name = trim($_POST['name']);
            $barcode = trim($_POST['barcode']);
            $category_id = $_POST['category_id'] ?: null;
            $description = trim($_POST['description']);
            $cost_price = floatval($_POST['cost_price']);
            $selling_price = floatval($_POST['selling_price']);
            $stock_quantity = intval($_POST['stock_quantity']);
            $min_stock_level = intval($_POST['min_stock_level']);
            $unit = trim($_POST['unit']);
            
            // التحقق من البيانات المطلوبة
            if (empty($name) || $selling_price <= 0) {
                throw new Exception('يرجى إدخال اسم المنتج والسعر');
            }
            
            // التحقق من عدم تكرار الباركود
            if (!empty($barcode)) {
                $stmt = $pdo->prepare("SELECT id FROM products WHERE barcode = ? AND id != ?");
                $stmt->execute([$barcode, $product_id ?: 0]);
                if ($stmt->fetch()) {
                    throw new Exception('هذا الباركود مستخدم بالفعل');
                }
            }
            
            // معالجة رفع الصورة
            $image_path = '';
            if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = 'uploads/products/';
                $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
                $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
                
                if (in_array($file_extension, $allowed_extensions)) {
                    $new_filename = uniqid() . '.' . $file_extension;
                    $upload_path = $upload_dir . $new_filename;
                    
                    if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                        $image_path = $upload_path;
                    }
                }
            }
            
            if ($action === 'add') {
                // إضافة منتج جديد
                $stmt = $pdo->prepare("
                    INSERT INTO products (name, barcode, category_id, description, cost_price, selling_price, 
                                        stock_quantity, min_stock_level, unit, image_path) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $name, $barcode, $category_id, $description, $cost_price, 
                    $selling_price, $stock_quantity, $min_stock_level, $unit, $image_path
                ]);
                
                $new_product_id = $pdo->lastInsertId();
                
                // تسجيل حركة المخزون الأولية
                if ($stock_quantity > 0) {
                    $stmt = $pdo->prepare("
                        INSERT INTO stock_movements (product_id, movement_type, quantity, reference_type, created_by) 
                        VALUES (?, 'in', ?, 'adjustment', ?)
                    ");
                    $stmt->execute([$new_product_id, $stock_quantity, $_SESSION['user_id']]);
                }
                
                $message = 'تم إضافة المنتج بنجاح';
                
            } else {
                // تعديل منتج موجود
                $sql = "UPDATE products SET name = ?, barcode = ?, category_id = ?, description = ?, 
                        cost_price = ?, selling_price = ?, min_stock_level = ?, unit = ?";
                $params = [$name, $barcode, $category_id, $description, $cost_price, $selling_price, $min_stock_level, $unit];
                
                if ($image_path) {
                    $sql .= ", image_path = ?";
                    $params[] = $image_path;
                }
                
                $sql .= " WHERE id = ?";
                $params[] = $product_id;
                
                $stmt = $pdo->prepare($sql);
                $stmt->execute($params);
                
                $message = 'تم تحديث المنتج بنجاح';
            }
            
        } elseif ($action === 'delete') {
            $product_id = $_POST['product_id'];
            
            // التحقق من عدم وجود مبيعات أو مشتريات للمنتج
            $stmt = $pdo->prepare("
                SELECT COUNT(*) FROM sale_items WHERE product_id = ?
                UNION ALL
                SELECT COUNT(*) FROM purchase_items WHERE product_id = ?
            ");
            $stmt->execute([$product_id, $product_id]);
            $has_transactions = array_sum($stmt->fetchAll(PDO::FETCH_COLUMN));
            
            if ($has_transactions > 0) {
                // إلغاء تفعيل المنتج بدلاً من حذفه
                $stmt = $pdo->prepare("UPDATE products SET is_active = 0 WHERE id = ?");
                $stmt->execute([$product_id]);
                $message = 'تم إلغاء تفعيل المنتج';
            } else {
                // حذف المنتج نهائياً
                $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
                $stmt->execute([$product_id]);
                $message = 'تم حذف المنتج';
            }
            
        } elseif ($action === 'adjust_stock') {
            $product_id = $_POST['product_id'];
            $new_quantity = intval($_POST['new_quantity']);
            $notes = trim($_POST['notes']);
            
            // الحصول على الكمية الحالية
            $stmt = $pdo->prepare("SELECT stock_quantity FROM products WHERE id = ?");
            $stmt->execute([$product_id]);
            $current_quantity = $stmt->fetchColumn();
            
            if ($current_quantity !== false) {
                // تحديث الكمية
                $stmt = $pdo->prepare("UPDATE products SET stock_quantity = ? WHERE id = ?");
                $stmt->execute([$new_quantity, $product_id]);
                
                // تسجيل حركة المخزون
                $movement_type = $new_quantity > $current_quantity ? 'in' : 'out';
                $quantity_diff = abs($new_quantity - $current_quantity);
                
                $stmt = $pdo->prepare("
                    INSERT INTO stock_movements (product_id, movement_type, quantity, reference_type, notes, created_by) 
                    VALUES (?, ?, ?, 'adjustment', ?, ?)
                ");
                $stmt->execute([$product_id, $movement_type, $quantity_diff, $notes, $_SESSION['user_id']]);
                
                $message = 'تم تعديل المخزون بنجاح';
            }
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// جلب البيانات للعرض
if ($action === 'edit' && $product_id) {
    $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ?");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch();
    
    if (!$product) {
        $error = 'المنتج غير موجود';
        $action = 'list';
    }
}

// جلب الفئات
$stmt = $pdo->prepare("SELECT * FROM categories WHERE is_active = 1 ORDER BY name");
$stmt->execute();
$categories = $stmt->fetchAll();

// جلب المنتجات للعرض
if ($action === 'list') {
    $search = $_GET['search'] ?? '';
    $category_filter = $_GET['category'] ?? '';
    $stock_filter = $_GET['stock_filter'] ?? '';
    
    $sql = "SELECT p.*, c.name as category_name FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE p.is_active = 1";
    $params = [];
    
    if ($search) {
        $sql .= " AND (p.name LIKE ? OR p.barcode LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if ($category_filter) {
        $sql .= " AND p.category_id = ?";
        $params[] = $category_filter;
    }
    
    if ($stock_filter === 'low') {
        $sql .= " AND p.stock_quantity <= p.min_stock_level";
    } elseif ($stock_filter === 'out') {
        $sql .= " AND p.stock_quantity = 0";
    }
    
    $sql .= " ORDER BY p.name";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $products = $stmt->fetchAll();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات - نظام إدارة المحل Z</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <?php if ($action === 'add'): ?>
                            إضافة منتج جديد
                        <?php elseif ($action === 'edit'): ?>
                            تعديل المنتج
                        <?php else: ?>
                            إدارة المنتجات
                        <?php endif; ?>
                    </h1>
                    
                    <?php if ($action === 'list'): ?>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="products.php?action=add" class="btn btn-sm btn-primary">
                                <i class="fas fa-plus"></i> إضافة منتج
                            </a>
                            <a href="barcode.php" class="btn btn-sm btn-secondary">
                                <i class="fas fa-barcode"></i> إنتاج باركود
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
                
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($action === 'add' || $action === 'edit'): ?>
                    <!-- نموذج إضافة/تعديل المنتج -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <?= $action === 'add' ? 'إضافة منتج جديد' : 'تعديل المنتج' ?>
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">اسم المنتج *</label>
                                            <input type="text" class="form-control" name="name" 
                                                   value="<?= htmlspecialchars($product['name'] ?? '') ?>" required>
                                            <div class="invalid-feedback">يرجى إدخال اسم المنتج</div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الباركود</label>
                                            <input type="text" class="form-control" name="barcode" 
                                                   value="<?= htmlspecialchars($product['barcode'] ?? '') ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الفئة</label>
                                            <select class="form-select" name="category_id">
                                                <option value="">اختر الفئة</option>
                                                <?php foreach ($categories as $category): ?>
                                                    <option value="<?= $category['id'] ?>" 
                                                            <?= ($product['category_id'] ?? '') == $category['id'] ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($category['name']) ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الوحدة</label>
                                            <input type="text" class="form-control" name="unit" 
                                                   value="<?= htmlspecialchars($product['unit'] ?? 'قطعة') ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" name="description" rows="3"><?= htmlspecialchars($product['description'] ?? '') ?></textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">سعر التكلفة</label>
                                            <input type="number" class="form-control" name="cost_price" step="0.01" min="0"
                                                   value="<?= $product['cost_price'] ?? '0' ?>">
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">سعر البيع *</label>
                                            <input type="number" class="form-control" name="selling_price" step="0.01" min="0.01"
                                                   value="<?= $product['selling_price'] ?? '' ?>" required>
                                            <div class="invalid-feedback">يرجى إدخال سعر البيع</div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">الكمية في المخزون</label>
                                            <input type="number" class="form-control" name="stock_quantity" min="0"
                                                   value="<?= $product['stock_quantity'] ?? '0' ?>">
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">الحد الأدنى للمخزون</label>
                                            <input type="number" class="form-control" name="min_stock_level" min="0"
                                                   value="<?= $product['min_stock_level'] ?? '5' ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">صورة المنتج</label>
                                    <input type="file" class="form-control" name="image" accept="image/*">
                                    <?php if (!empty($product['image_path'])): ?>
                                        <div class="mt-2">
                                            <img src="<?= htmlspecialchars($product['image_path']) ?>" 
                                                 alt="صورة المنتج" style="max-width: 100px; max-height: 100px;">
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i>
                                        <?= $action === 'add' ? 'إضافة المنتج' : 'حفظ التغييرات' ?>
                                    </button>
                                    <a href="products.php" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> إلغاء
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                <?php else: ?>
                    <!-- قائمة المنتجات -->
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h5 class="mb-0">قائمة المنتجات</h5>
                                </div>
                                <div class="col-md-6">
                                    <form method="get" class="d-flex gap-2">
                                        <input type="text" class="form-control form-control-sm" name="search" 
                                               placeholder="البحث..." value="<?= htmlspecialchars($_GET['search'] ?? '') ?>">
                                        <select class="form-select form-select-sm" name="category">
                                            <option value="">جميع الفئات</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?= $category['id'] ?>" 
                                                        <?= ($_GET['category'] ?? '') == $category['id'] ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($category['name']) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <select class="form-select form-select-sm" name="stock_filter">
                                            <option value="">جميع المنتجات</option>
                                            <option value="low" <?= ($_GET['stock_filter'] ?? '') === 'low' ? 'selected' : '' ?>>مخزون منخفض</option>
                                            <option value="out" <?= ($_GET['stock_filter'] ?? '') === 'out' ? 'selected' : '' ?>>نفد المخزون</option>
                                        </select>
                                        <button type="submit" class="btn btn-sm btn-primary">بحث</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>الصورة</th>
                                            <th>اسم المنتج</th>
                                            <th>الباركود</th>
                                            <th>الفئة</th>
                                            <th>سعر البيع</th>
                                            <th>المخزون</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (empty($products)): ?>
                                            <tr>
                                                <td colspan="8" class="text-center text-muted py-4">
                                                    لا توجد منتجات
                                                </td>
                                            </tr>
                                        <?php else: ?>
                                            <?php foreach ($products as $product): ?>
                                                <tr>
                                                    <td>
                                                        <?php if ($product['image_path']): ?>
                                                            <img src="<?= htmlspecialchars($product['image_path']) ?>" 
                                                                 alt="صورة المنتج" style="width: 40px; height: 40px; object-fit: cover; border-radius: 5px;">
                                                        <?php else: ?>
                                                            <div style="width: 40px; height: 40px; background: #f8f9fa; border-radius: 5px; display: flex; align-items: center; justify-content: center;">
                                                                <i class="fas fa-image text-muted"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <strong><?= htmlspecialchars($product['name']) ?></strong>
                                                        <?php if ($product['description']): ?>
                                                            <br><small class="text-muted"><?= htmlspecialchars(substr($product['description'], 0, 50)) ?>...</small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?= htmlspecialchars($product['barcode']) ?></td>
                                                    <td><?= htmlspecialchars($product['category_name'] ?? 'غير محدد') ?></td>
                                                    <td><?= number_format($product['selling_price'], 2) ?> ج.م</td>
                                                    <td>
                                                        <span class="badge <?= $product['stock_quantity'] <= $product['min_stock_level'] ? 'bg-warning' : 'bg-success' ?>">
                                                            <?= $product['stock_quantity'] ?> <?= htmlspecialchars($product['unit']) ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <?php if ($product['stock_quantity'] == 0): ?>
                                                            <span class="badge bg-danger">نفد المخزون</span>
                                                        <?php elseif ($product['stock_quantity'] <= $product['min_stock_level']): ?>
                                                            <span class="badge bg-warning">مخزون منخفض</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-success">متوفر</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <div class="action-buttons">
                                                            <a href="products.php?action=edit&id=<?= $product['id'] ?>" 
                                                               class="btn btn-sm btn-primary" title="تعديل">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <button type="button" class="btn btn-sm btn-info" 
                                                                    onclick="adjustStock(<?= $product['id'] ?>, '<?= htmlspecialchars($product['name']) ?>', <?= $product['stock_quantity'] ?>)" 
                                                                    title="تعديل المخزون">
                                                                <i class="fas fa-boxes"></i>
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-danger" 
                                                                    onclick="deleteProduct(<?= $product['id'] ?>, '<?= htmlspecialchars($product['name']) ?>')" 
                                                                    title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>
    
    <!-- مودال تعديل المخزون -->
    <div class="modal fade" id="adjustStockModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل المخزون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="post">
                    <input type="hidden" name="action" value="adjust_stock">
                    <input type="hidden" name="product_id" id="adjust_product_id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">المنتج</label>
                            <input type="text" class="form-control" id="adjust_product_name" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الكمية الحالية</label>
                            <input type="text" class="form-control" id="adjust_current_quantity" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الكمية الجديدة</label>
                            <input type="number" class="form-control" name="new_quantity" id="adjust_new_quantity" min="0" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        function adjustStock(productId, productName, currentQuantity) {
            document.getElementById('adjust_product_id').value = productId;
            document.getElementById('adjust_product_name').value = productName;
            document.getElementById('adjust_current_quantity').value = currentQuantity;
            document.getElementById('adjust_new_quantity').value = currentQuantity;
            
            new bootstrap.Modal(document.getElementById('adjustStockModal')).show();
        }
        
        function deleteProduct(productId, productName) {
            if (confirm(`هل أنت متأكد من حذف المنتج "${productName}"؟`)) {
                const form = document.createElement('form');
                form.method = 'post';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="product_id" value="${productId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        // تفعيل التحقق من صحة النماذج
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
</body>
</html>
