<?php
// جلب إعدادات المتجر
try {
    $stmt = $pdo->prepare("SELECT setting_name, setting_value FROM settings");
    $stmt->execute();
    $settings = [];
    while ($row = $stmt->fetch()) {
        $settings[$row['setting_name']] = $row['setting_value'];
    }
} catch (PDOException $e) {
    $settings = ['store_name' => 'محل Z'];
}
?>
<nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
    <div class="container-fluid">
        <a class="navbar-brand d-flex align-items-center" href="index.php">
            <div class="logo-icon me-2">Z</div>
            <span><?= htmlspecialchars($settings['store_name'] ?? 'محل Z') ?></span>
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="pos.php">
                        <i class="fas fa-cash-register"></i> نقطة البيع
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="productsDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-boxes"></i> المنتجات
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="products.php">عرض المنتجات</a></li>
                        <li><a class="dropdown-item" href="products.php?action=add">إضافة منتج</a></li>
                        <li><a class="dropdown-item" href="categories.php">الفئات</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="barcode.php">إنتاج باركود</a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="salesDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-shopping-bag"></i> المبيعات
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="sales.php">عرض المبيعات</a></li>
                        <li><a class="dropdown-item" href="pos.php">بيع جديد</a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="purchasesDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-shopping-cart"></i> المشتريات
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="purchases.php">عرض المشتريات</a></li>
                        <li><a class="dropdown-item" href="purchases.php?action=add">مشتريات جديدة</a></li>
                    </ul>
                </li>
            </ul>
            
            <ul class="navbar-nav">
                <!-- البحث السريع -->
                <li class="nav-item">
                    <form class="d-flex me-3" id="quickSearchForm">
                        <div class="input-group">
                            <input class="form-control form-control-sm" type="search" placeholder="بحث سريع..." id="quickSearch">
                            <button class="btn btn-outline-light btn-sm" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </li>
                
                <!-- الإشعارات -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle position-relative" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        <?php
                        // عدد المنتجات منخفضة المخزون
                        try {
                            $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE stock_quantity <= min_stock_level AND is_active = 1");
                            $stmt->execute();
                            $notifications_count = $stmt->fetchColumn();
                            if ($notifications_count > 0):
                        ?>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                            <?= $notifications_count ?>
                        </span>
                        <?php endif; } catch (PDOException $e) {} ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><h6 class="dropdown-header">الإشعارات</h6></li>
                        <?php
                        try {
                            $stmt = $pdo->prepare("SELECT name, stock_quantity, min_stock_level FROM products WHERE stock_quantity <= min_stock_level AND is_active = 1 LIMIT 5");
                            $stmt->execute();
                            $low_stock_products = $stmt->fetchAll();
                            
                            if ($low_stock_products):
                                foreach ($low_stock_products as $product):
                        ?>
                        <li>
                            <a class="dropdown-item" href="products.php?id=<?= $product['id'] ?>">
                                <i class="fas fa-exclamation-triangle text-warning"></i>
                                <?= htmlspecialchars($product['name']) ?>
                                <small class="text-muted d-block">المخزون: <?= $product['stock_quantity'] ?></small>
                            </a>
                        </li>
                        <?php
                                endforeach;
                            else:
                        ?>
                        <li><span class="dropdown-item-text">لا توجد إشعارات</span></li>
                        <?php endif; } catch (PDOException $e) {} ?>
                    </ul>
                </li>
                
                <!-- قائمة المستخدم -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i>
                        <?= htmlspecialchars($_SESSION['full_name']) ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="profile.php">
                            <i class="fas fa-user-cog"></i> الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="settings.php">
                            <i class="fas fa-cog"></i> الإعدادات
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="backup.php">
                            <i class="fas fa-download"></i> نسخ احتياطي
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php">
                            <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<style>
.logo-icon {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
}

.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.2rem;
}

.nav-link {
    transition: all 0.3s ease;
}

.nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    border-radius: 5px;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border-radius: 8px;
}

.dropdown-item {
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

body {
    padding-top: 76px;
}

#quickSearch {
    width: 200px;
}

@media (max-width: 768px) {
    #quickSearch {
        width: 150px;
    }
}
</style>

<script>
// البحث السريع
document.getElementById('quickSearchForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const searchTerm = document.getElementById('quickSearch').value.trim();
    if (searchTerm) {
        window.location.href = `search.php?q=${encodeURIComponent(searchTerm)}`;
    }
});

// البحث أثناء الكتابة
let searchTimeout;
document.getElementById('quickSearch').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    const searchTerm = this.value.trim();
    
    if (searchTerm.length >= 2) {
        searchTimeout = setTimeout(() => {
            // يمكن إضافة البحث المباشر هنا
        }, 500);
    }
});
</script>
