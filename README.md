# نظام إدارة المحل Z

نظام شامل لإدارة المحلات التجارية مبني بـ PHP مع قاعدة بيانات MySQL، يوفر جميع الأدوات اللازمة لإدارة المبيعات والمشتريات والمخزون والعملاء.

## المميزات الرئيسية

### 🛒 نقطة البيع (POS)
- واجهة سهلة الاستخدام لعمليات البيع السريعة
- دعم قارئ الباركود
- إدارة ذكية للرصيد المحفوظ والديون
- حساب الفكة تلقائياً
- دعم طرق دفع متعددة (نقدي، آجل، رصيد محفوظ، مختلط)

### 📦 إدارة المنتجات
- إضافة وتعديل المنتجات مع الصور
- تصنيف المنتجات في فئات
- تتبع المخزون مع تنبيهات المخزون المنخفض
- إنتاج وطباعة الباركود

### 👥 إدارة العملاء
- نظام الرصيد المحفوظ (الفكة)
- تتبع الديون والمدفوعات
- ربط أرقام الواتساب لإرسال الفواتير
- تاريخ المشتريات والإحصائيات

### 🚚 إدارة الموردين والمشتريات
- إدارة بيانات الموردين
- تسجيل فواتير المشتريات
- تحديث المخزون تلقائياً
- تتبع المدفوعات للموردين

### 💰 النظام المالي
- إدارة الخزنة مع تتبع جميع الحركات
- تقارير مالية شاملة
- تسجيل المصروفات
- حساب الأرباح والخسائر

### 📱 إرسال الفواتير عبر الواتساب
- إرسال تفاصيل الفاتورة مباشرة للعميل
- رسائل مخصصة مع بيانات المحل
- ربط تلقائي مع أرقام العملاء

### 🔍 البحث المتقدم
- بحث سريع في جميع أجزاء النظام
- فلترة متقدمة للنتائج
- بحث بالباركود والاسم

### 🛡️ النسخ الاحتياطي والأمان
- إنشاء نسخ احتياطية تلقائية
- استعادة البيانات
- إعادة ضبط المصنع
- نظام مستخدمين متعدد المستويات

## متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx
- امتدادات PHP المطلوبة:
  - MySQLi
  - PDO
  - GD (لإنتاج الباركود)
  - JSON

## التثبيت

### 1. تحميل الملفات
```bash
git clone https://github.com/your-repo/z-store.git
cd z-store
```

### 2. إعداد الخادم المحلي
- ضع الملفات في مجلد الخادم المحلي (مثل `htdocs` في XAMPP)
- تأكد من تشغيل Apache و MySQL

### 3. تشغيل معالج التثبيت
1. افتح المتصفح واذهب إلى `http://localhost/z-store/install.php`
2. اتبع خطوات التثبيت:
   - التحقق من المتطلبات
   - إعداد قاعدة البيانات
   - إعداد بيانات المتجر والمدير

### 4. تسجيل الدخول
- اسم المستخدم: `admin` (أو ما تم تحديده أثناء التثبيت)
- كلمة المرور: ما تم تحديده أثناء التثبيت

## الاستخدام

### البدء السريع

1. **إعداد المنتجات**:
   - اذهب إلى "المنتجات" → "إضافة منتج"
   - أدخل بيانات المنتج والسعر
   - أنتج باركود للمنتج

2. **إضافة عملاء**:
   - اذهب إلى "العملاء" → "إضافة عميل"
   - أدخل بيانات العميل ورقم الواتساب

3. **بدء البيع**:
   - اذهب إلى "نقطة البيع"
   - اختر المنتجات أو استخدم قارئ الباركود
   - اختر العميل وطريقة الدفع
   - أتمم البيع

### نظام الرصيد والديون

#### الرصيد المحفوظ (الفكة)
- عندما يدفع العميل مبلغاً أكبر من الفاتورة، يتم حفظ الفكة كرصيد
- يمكن استخدام هذا الرصيد في مشتريات لاحقة
- عند وصول الرصيد لـ 10 جنيه أو أكثر، يعرض النظام خيار الاستخدام

#### إدارة الديون
- يمكن البيع بالدين للعملاء المسجلين
- يتم خصم الرصيد المحفوظ تلقائياً من الديون
- تتبع جميع المدفوعات والديون المستحقة

### إرسال الفواتير عبر الواتساب

1. تأكد من إدخال رقم الواتساب للعميل بالصيغة الدولية (مثل: 201234567890)
2. بعد إتمام البيع، اضغط على "إرسال واتساب"
3. سيتم فتح الواتساب مع رسالة جاهزة تحتوي على تفاصيل الفاتورة

### إنتاج الباركود

1. اذهب إلى "إنتاج باركود"
2. اختر المنتج أو مجموعة منتجات
3. اختر نوع الباركود (CODE128 موصى به)
4. اطبع الباركود أو حمله كصورة

## الهيكل التقني

### قاعدة البيانات
- `products` - المنتجات
- `customers` - العملاء
- `suppliers` - الموردين
- `sales` - المبيعات
- `purchases` - المشتريات
- `cash_register` - حركات الخزنة
- `stock_movements` - حركات المخزون

### الملفات الرئيسية
- `index.php` - الصفحة الرئيسية
- `pos.php` - نقطة البيع
- `products.php` - إدارة المنتجات
- `customers.php` - إدارة العملاء
- `barcode.php` - إنتاج الباركود
- `backup.php` - النسخ الاحتياطي

## الأمان

- تشفير كلمات المرور باستخدام `password_hash()`
- حماية من SQL Injection باستخدام Prepared Statements
- نظام جلسات آمن
- التحقق من الصلاحيات لكل صفحة

## النسخ الاحتياطي

### إنشاء نسخة احتياطية
1. اذهب إلى "النسخ الاحتياطي"
2. اضغط "إنشاء نسخة احتياطية الآن"
3. سيتم حفظ الملف في مجلد `backups`

### استعادة نسخة احتياطية
1. اذهب إلى "النسخ الاحتياطي"
2. اختر "استعادة نسخة احتياطية"
3. ارفع ملف النسخة الاحتياطية (.sql)

## إعادة ضبط المصنع

⚠️ **تحذير**: هذا الإجراء سيحذف جميع البيانات نهائياً!

1. اذهب إلى "النسخ الاحتياطي"
2. انتقل لقسم "إعادة ضبط المصنع"
3. اكتب "RESET" للتأكيد
4. اضغط "إعادة ضبط المصنع"

## الدعم والمساعدة

### المشاكل الشائعة

**مشكلة**: لا يعمل قارئ الباركود
**الحل**: تأكد من تفعيل قارئ الباركود من نقطة البيع وأن الباركود صحيح

**مشكلة**: لا يتم إرسال الواتساب
**الحل**: تأكد من إدخال رقم الواتساب بالصيغة الدولية الصحيحة

**مشكلة**: خطأ في قاعدة البيانات
**الحل**: تحقق من إعدادات قاعدة البيانات في ملف `config.php`

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## المساهمة

نرحب بالمساهمات! يرجى قراءة [CONTRIBUTING.md](CONTRIBUTING.md) للتفاصيل.

## التطوير المستقبلي

- [ ] تطبيق موبايل
- [ ] تقارير متقدمة مع الرسوم البيانية
- [ ] دعم العملات المتعددة
- [ ] نظام الخصومات المتقدم
- [ ] ربط مع منصات التجارة الإلكترونية

---

**نظام إدارة المحل Z** - حل شامل لإدارة محلك التجاري 🏪
