<?php
/**
 * نظام إدارة المحل Z - نقطة البيع (POS)
 */

require_once 'config.php';
require_login();

$message = '';
$error = '';
$sale_id = null;

// معالجة عملية البيع
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'complete_sale') {
    try {
        $pdo->beginTransaction();
        
        $customer_id = $_POST['customer_id'] ?: null;
        $cart_items = json_decode($_POST['cart_items'], true);
        $total_amount = floatval($_POST['total_amount']);
        $paid_amount = floatval($_POST['paid_amount']);
        $wallet_used = floatval($_POST['wallet_used']);
        $payment_type = $_POST['payment_type'];
        $discount_amount = floatval($_POST['discount_amount']);
        
        if (empty($cart_items) || $total_amount <= 0) {
            throw new Exception('السلة فارغة أو المبلغ غير صحيح');
        }
        
        // التحقق من توفر المنتجات في المخزون
        foreach ($cart_items as $item) {
            $stmt = $pdo->prepare("SELECT stock_quantity FROM products WHERE id = ?");
            $stmt->execute([$item['id']]);
            $stock = $stmt->fetchColumn();
            
            if ($stock < $item['quantity']) {
                throw new Exception("المنتج {$item['name']} غير متوفر بالكمية المطلوبة");
            }
        }
        
        // حساب المبالغ
        $final_amount = $total_amount - $discount_amount;
        $remaining_amount = $final_amount - $paid_amount - $wallet_used;
        $change_amount = $remaining_amount < 0 ? abs($remaining_amount) : 0;
        $debt_amount = $remaining_amount > 0 ? $remaining_amount : 0;
        
        // تحديد حالة الدفع
        if ($debt_amount > 0) {
            $payment_status = 'unpaid';
        } elseif ($paid_amount + $wallet_used < $final_amount) {
            $payment_status = 'partial';
        } else {
            $payment_status = 'paid';
        }
        
        // إنشاء رقم الفاتورة
        $invoice_number = 'INV-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        // إدراج فاتورة البيع
        $stmt = $pdo->prepare("
            INSERT INTO sales (invoice_number, customer_id, total_amount, paid_amount, wallet_used, 
                             remaining_amount, change_amount, payment_type, payment_status, 
                             discount_amount, sale_date, created_by) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURDATE(), ?)
        ");
        $stmt->execute([
            $invoice_number, $customer_id, $final_amount, $paid_amount, $wallet_used,
            $debt_amount, $change_amount, $payment_type, $payment_status,
            $discount_amount, $_SESSION['user_id']
        ]);
        
        $sale_id = $pdo->lastInsertId();
        
        // إدراج تفاصيل البيع وتحديث المخزون
        foreach ($cart_items as $item) {
            // إدراج تفاصيل البيع
            $stmt = $pdo->prepare("
                INSERT INTO sale_items (sale_id, product_id, quantity, unit_price, total_price) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $sale_id, $item['id'], $item['quantity'], 
                $item['price'], $item['price'] * $item['quantity']
            ]);
            
            // تحديث المخزون
            $stmt = $pdo->prepare("
                UPDATE products SET stock_quantity = stock_quantity - ? WHERE id = ?
            ");
            $stmt->execute([$item['quantity'], $item['id']]);
            
            // تسجيل حركة المخزون
            $stmt = $pdo->prepare("
                INSERT INTO stock_movements (product_id, movement_type, quantity, reference_type, reference_id, created_by) 
                VALUES (?, 'out', ?, 'sale', ?, ?)
            ");
            $stmt->execute([$item['id'], $item['quantity'], $sale_id, $_SESSION['user_id']]);
        }
        
        // تحديث بيانات العميل
        if ($customer_id) {
            $stmt = $pdo->prepare("SELECT wallet_balance, debt_balance, total_purchases FROM customers WHERE id = ?");
            $stmt->execute([$customer_id]);
            $customer_data = $stmt->fetch();
            
            $new_wallet_balance = $customer_data['wallet_balance'] - $wallet_used + $change_amount;
            $new_debt_balance = $customer_data['debt_balance'] + $debt_amount;
            $new_total_purchases = $customer_data['total_purchases'] + $final_amount;
            
            $stmt = $pdo->prepare("
                UPDATE customers 
                SET wallet_balance = ?, debt_balance = ?, total_purchases = ? 
                WHERE id = ?
            ");
            $stmt->execute([$new_wallet_balance, $new_debt_balance, $new_total_purchases, $customer_id]);
        }
        
        // تحديث الخزنة
        if ($paid_amount > 0) {
            $stmt = $pdo->prepare("SELECT balance_after FROM cash_register ORDER BY id DESC LIMIT 1");
            $stmt->execute();
            $current_balance = $stmt->fetchColumn() ?: 0;
            
            $stmt = $pdo->prepare("
                INSERT INTO cash_register (transaction_type, amount, balance_before, balance_after, 
                                         reference_type, reference_id, description, created_by) 
                VALUES ('sale', ?, ?, ?, 'sale', ?, ?, ?)
            ");
            $stmt->execute([
                $paid_amount, $current_balance, $current_balance + $paid_amount,
                $sale_id, "بيع - فاتورة رقم: $invoice_number", $_SESSION['user_id']
            ]);
        }
        
        $pdo->commit();
        $message = 'تم إتمام عملية البيع بنجاح';
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $error = $e->getMessage();
    }
}

// جلب المنتجات النشطة
$stmt = $pdo->prepare("
    SELECT p.*, c.name as category_name 
    FROM products p 
    LEFT JOIN categories c ON p.category_id = c.id 
    WHERE p.is_active = 1 AND p.stock_quantity > 0 
    ORDER BY p.name
");
$stmt->execute();
$products = $stmt->fetchAll();

// جلب العملاء النشطين
$stmt = $pdo->prepare("SELECT id, name, phone, whatsapp, wallet_balance, debt_balance FROM customers WHERE is_active = 1 ORDER BY name");
$stmt->execute();
$customers = $stmt->fetchAll();

// جلب الفئات
$stmt = $pdo->prepare("SELECT * FROM categories WHERE is_active = 1 ORDER BY name");
$stmt->execute();
$categories = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نقطة البيع - نظام إدارة المحل Z</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        .pos-container {
            min-height: calc(100vh - 120px);
        }
        .product-card {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid #e9ecef;
        }
        .product-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .product-card.out-of-stock {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .cart-container {
            position: sticky;
            top: 100px;
            max-height: calc(100vh - 120px);
            overflow-y: auto;
        }
        .cart-item {
            border-bottom: 1px solid #e9ecef;
            padding: 10px 0;
        }
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .quantity-btn {
            width: 30px;
            height: 30px;
            border: none;
            border-radius: 50%;
            background: #667eea;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        .barcode-scanner {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">نقطة البيع</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-success" onclick="clearCart()">
                                <i class="fas fa-trash"></i> مسح السلة
                            </button>
                            <button type="button" class="btn btn-sm btn-info" onclick="toggleBarcodeScanner()">
                                <i class="fas fa-barcode"></i> قارئ الباركود
                            </button>
                        </div>
                    </div>
                </div>
                
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
                        <?php if ($sale_id): ?>
                            <div class="mt-2">
                                <a href="print_invoice.php?id=<?= $sale_id ?>" class="btn btn-sm btn-primary" target="_blank">
                                    <i class="fas fa-print"></i> طباعة الفاتورة
                                </a>
                                <button type="button" class="btn btn-sm btn-success" onclick="sendWhatsApp(<?= $sale_id ?>)">
                                    <i class="fab fa-whatsapp"></i> إرسال واتساب
                                </button>
                            </div>
                        <?php endif; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <div class="row pos-container">
                    <!-- قسم المنتجات -->
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h5 class="mb-0">المنتجات</h5>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-flex gap-2">
                                            <input type="text" class="form-control form-control-sm" 
                                                   id="product-search" placeholder="البحث عن منتج أو باركود...">
                                            <select class="form-select form-select-sm" id="category-filter">
                                                <option value="">جميع الفئات</option>
                                                <?php foreach ($categories as $category): ?>
                                                    <option value="<?= $category['id'] ?>"><?= htmlspecialchars($category['name']) ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row" id="products-grid">
                                    <?php foreach ($products as $product): ?>
                                        <div class="col-xl-3 col-lg-4 col-md-6 mb-3 product-item" 
                                             data-category="<?= $product['category_id'] ?>"
                                             data-name="<?= strtolower($product['name']) ?>"
                                             data-barcode="<?= $product['barcode'] ?>">
                                            <div class="card product-card <?= $product['stock_quantity'] <= 0 ? 'out-of-stock' : '' ?>" 
                                                 onclick="<?= $product['stock_quantity'] > 0 ? "addToCart({$product['id']}, '{$product['name']}', {$product['selling_price']}, {$product['stock_quantity']})" : '' ?>">
                                                <div class="card-body text-center p-3">
                                                    <?php if ($product['image_path']): ?>
                                                        <img src="<?= htmlspecialchars($product['image_path']) ?>" 
                                                             alt="<?= htmlspecialchars($product['name']) ?>" 
                                                             class="img-fluid mb-2" style="max-height: 80px;">
                                                    <?php else: ?>
                                                        <div class="bg-light d-flex align-items-center justify-content-center mb-2" 
                                                             style="height: 80px; border-radius: 5px;">
                                                            <i class="fas fa-box fa-2x text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <h6 class="card-title mb-1"><?= htmlspecialchars($product['name']) ?></h6>
                                                    <p class="card-text mb-1">
                                                        <strong class="text-primary"><?= number_format($product['selling_price'], 2) ?> ج.م</strong>
                                                    </p>
                                                    <small class="text-muted">
                                                        المخزون: <?= $product['stock_quantity'] ?> <?= htmlspecialchars($product['unit']) ?>
                                                    </small>
                                                    <?php if ($product['stock_quantity'] <= $product['min_stock_level']): ?>
                                                        <br><span class="badge bg-warning">مخزون منخفض</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- قسم السلة والدفع -->
                    <div class="col-lg-4">
                        <div class="cart-container">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">سلة التسوق</h5>
                                </div>
                                <div class="card-body">
                                    <!-- اختيار العميل -->
                                    <div class="mb-3">
                                        <label class="form-label">العميل</label>
                                        <select class="form-select" id="customer-select" onchange="updateCustomerInfo()">
                                            <option value="">عميل نقدي</option>
                                            <?php foreach ($customers as $customer): ?>
                                                <option value="<?= $customer['id'] ?>" 
                                                        data-wallet="<?= $customer['wallet_balance'] ?>"
                                                        data-debt="<?= $customer['debt_balance'] ?>"
                                                        data-whatsapp="<?= $customer['whatsapp'] ?>">
                                                    <?= htmlspecialchars($customer['name']) ?>
                                                    <?php if ($customer['phone']): ?>
                                                        - <?= htmlspecialchars($customer['phone']) ?>
                                                    <?php endif; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <!-- معلومات العميل -->
                                    <div id="customer-info" class="mb-3" style="display: none;">
                                        <div class="row">
                                            <div class="col-6">
                                                <small class="text-muted">الرصيد المحفوظ:</small>
                                                <div class="fw-bold text-success" id="customer-wallet">0.00 ج.م</div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">الديون:</small>
                                                <div class="fw-bold text-warning" id="customer-debt">0.00 ج.م</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- عناصر السلة -->
                                    <div id="cart-items" class="mb-3">
                                        <div class="text-center text-muted py-4">
                                            السلة فارغة
                                        </div>
                                    </div>
                                    
                                    <!-- الخصم -->
                                    <div class="mb-3">
                                        <label class="form-label">الخصم</label>
                                        <input type="number" class="form-control" id="discount-amount" 
                                               step="0.01" min="0" value="0" onchange="updateTotals()">
                                    </div>
                                    
                                    <!-- الإجماليات -->
                                    <div class="border-top pt-3">
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>المجموع الفرعي:</span>
                                            <span id="subtotal">0.00 ج.م</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>الخصم:</span>
                                            <span id="discount-display">0.00 ج.م</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-3 fw-bold">
                                            <span>الإجمالي:</span>
                                            <span id="total">0.00 ج.م</span>
                                        </div>
                                        
                                        <!-- خيارات الدفع -->
                                        <div class="mb-3">
                                            <label class="form-label">طريقة الدفع</label>
                                            <select class="form-select" id="payment-type" onchange="updatePaymentOptions()">
                                                <option value="cash">نقدي</option>
                                                <option value="credit">آجل (دين)</option>
                                                <option value="wallet">من الرصيد المحفوظ</option>
                                                <option value="mixed">مختلط</option>
                                            </select>
                                        </div>
                                        
                                        <!-- المبلغ المدفوع -->
                                        <div class="mb-3" id="paid-amount-container">
                                            <label class="form-label">المبلغ المدفوع</label>
                                            <input type="number" class="form-control" id="paid-amount" 
                                                   step="0.01" min="0" onchange="calculateChange()">
                                        </div>
                                        
                                        <!-- استخدام الرصيد المحفوظ -->
                                        <div class="mb-3" id="wallet-usage-container" style="display: none;">
                                            <label class="form-label">استخدام من الرصيد المحفوظ</label>
                                            <input type="number" class="form-control" id="wallet-used" 
                                                   step="0.01" min="0" max="0" onchange="calculateChange()">
                                        </div>
                                        
                                        <!-- الباقي/الفكة -->
                                        <div class="mb-3" id="change-container" style="display: none;">
                                            <div class="d-flex justify-content-between">
                                                <span id="change-label">الفكة:</span>
                                                <span id="change-amount" class="fw-bold">0.00 ج.م</span>
                                            </div>
                                        </div>
                                        
                                        <!-- أزرار الإجراءات -->
                                        <div class="d-grid gap-2">
                                            <button type="button" class="btn btn-success btn-lg" 
                                                    id="complete-sale-btn" onclick="completeSale()" disabled>
                                                <i class="fas fa-check"></i> إتمام البيع
                                            </button>
                                            <button type="button" class="btn btn-secondary" onclick="clearCart()">
                                                <i class="fas fa-trash"></i> مسح السلة
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- مؤشر قارئ الباركود -->
    <div class="barcode-scanner" id="barcode-indicator" style="display: none;">
        <div class="btn btn-warning">
            <i class="fas fa-barcode fa-blink"></i> قارئ الباركود نشط
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // متغيرات عامة
        let cart = [];
        let products = <?= json_encode($products) ?>;
        let customers = <?= json_encode($customers) ?>;
        let barcodeMode = false;
        let barcodeBuffer = '';
        let barcodeTimeout;

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateCartDisplay();
            initializeSearch();
            initializeBarcodeScanner();
        });

        // تهيئة البحث
        function initializeSearch() {
            const searchInput = document.getElementById('product-search');
            const categoryFilter = document.getElementById('category-filter');

            searchInput.addEventListener('input', filterProducts);
            categoryFilter.addEventListener('change', filterProducts);
        }

        // فلترة المنتجات
        function filterProducts() {
            const searchTerm = document.getElementById('product-search').value.toLowerCase();
            const categoryId = document.getElementById('category-filter').value;
            const productItems = document.querySelectorAll('.product-item');

            productItems.forEach(item => {
                const name = item.dataset.name;
                const barcode = item.dataset.barcode;
                const category = item.dataset.category;

                const matchesSearch = !searchTerm || name.includes(searchTerm) || barcode.includes(searchTerm);
                const matchesCategory = !categoryId || category === categoryId;

                item.style.display = matchesSearch && matchesCategory ? 'block' : 'none';
            });
        }

        // إضافة منتج للسلة
        function addToCart(productId, productName, productPrice, stockQuantity) {
            const existingItem = cart.find(item => item.id === productId);

            if (existingItem) {
                if (existingItem.quantity < stockQuantity) {
                    existingItem.quantity++;
                } else {
                    alert('لا يمكن إضافة كمية أكثر من المتوفر في المخزون');
                    return;
                }
            } else {
                cart.push({
                    id: productId,
                    name: productName,
                    price: productPrice,
                    quantity: 1,
                    stock: stockQuantity
                });
            }

            updateCartDisplay();
            updateTotals();
        }

        // حذف منتج من السلة
        function removeFromCart(productId) {
            cart = cart.filter(item => item.id !== productId);
            updateCartDisplay();
            updateTotals();
        }

        // تحديث كمية المنتج
        function updateQuantity(productId, newQuantity) {
            const item = cart.find(item => item.id === productId);
            if (!item) return;

            if (newQuantity <= 0) {
                removeFromCart(productId);
                return;
            }

            if (newQuantity > item.stock) {
                alert('الكمية المطلوبة أكبر من المتوفر في المخزون');
                return;
            }

            item.quantity = newQuantity;
            updateCartDisplay();
            updateTotals();
        }

        // تحديث عرض السلة
        function updateCartDisplay() {
            const cartContainer = document.getElementById('cart-items');

            if (cart.length === 0) {
                cartContainer.innerHTML = '<div class="text-center text-muted py-4">السلة فارغة</div>';
                document.getElementById('complete-sale-btn').disabled = true;
                return;
            }

            cartContainer.innerHTML = cart.map(item => `
                <div class="cart-item">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${item.name}</h6>
                            <small class="text-muted">${formatCurrency(item.price)} × ${item.quantity}</small>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold">${formatCurrency(item.price * item.quantity)}</div>
                            <button class="btn btn-sm btn-outline-danger" onclick="removeFromCart(${item.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="quantity-controls">
                        <button class="quantity-btn" onclick="updateQuantity(${item.id}, ${item.quantity - 1})">
                            <i class="fas fa-minus"></i>
                        </button>
                        <input type="number" class="form-control form-control-sm text-center"
                               style="width: 70px;" value="${item.quantity}" min="1" max="${item.stock}"
                               onchange="updateQuantity(${item.id}, parseInt(this.value))">
                        <button class="quantity-btn" onclick="updateQuantity(${item.id}, ${item.quantity + 1})">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>
            `).join('');

            document.getElementById('complete-sale-btn').disabled = false;
        }

        // تحديث الإجماليات
        function updateTotals() {
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const discount = parseFloat(document.getElementById('discount-amount').value) || 0;
            const total = subtotal - discount;

            document.getElementById('subtotal').textContent = formatCurrency(subtotal);
            document.getElementById('discount-display').textContent = formatCurrency(discount);
            document.getElementById('total').textContent = formatCurrency(total);

            // تحديث المبلغ المدفوع الافتراضي
            const paidAmountInput = document.getElementById('paid-amount');
            if (!paidAmountInput.value || parseFloat(paidAmountInput.value) === 0) {
                paidAmountInput.value = total.toFixed(2);
            }

            calculateChange();
        }

        // تحديث معلومات العميل
        function updateCustomerInfo() {
            const customerSelect = document.getElementById('customer-select');
            const customerInfo = document.getElementById('customer-info');
            const walletUsageContainer = document.getElementById('wallet-usage-container');

            if (customerSelect.value) {
                const selectedOption = customerSelect.selectedOptions[0];
                const walletBalance = parseFloat(selectedOption.dataset.wallet) || 0;
                const debtBalance = parseFloat(selectedOption.dataset.debt) || 0;

                document.getElementById('customer-wallet').textContent = formatCurrency(walletBalance);
                document.getElementById('customer-debt').textContent = formatCurrency(debtBalance);

                // تحديث الحد الأقصى لاستخدام الرصيد
                const walletUsedInput = document.getElementById('wallet-used');
                walletUsedInput.max = walletBalance;

                customerInfo.style.display = 'block';
                updatePaymentOptions();
            } else {
                customerInfo.style.display = 'none';
                walletUsageContainer.style.display = 'none';
            }
        }

        // تحديث خيارات الدفع
        function updatePaymentOptions() {
            const paymentType = document.getElementById('payment-type').value;
            const customerSelect = document.getElementById('customer-select');
            const paidAmountContainer = document.getElementById('paid-amount-container');
            const walletUsageContainer = document.getElementById('wallet-usage-container');

            // إخفاء جميع الحاويات أولاً
            paidAmountContainer.style.display = 'none';
            walletUsageContainer.style.display = 'none';

            if (paymentType === 'cash') {
                paidAmountContainer.style.display = 'block';
            } else if (paymentType === 'credit') {
                if (!customerSelect.value) {
                    alert('يجب اختيار عميل للبيع الآجل');
                    document.getElementById('payment-type').value = 'cash';
                    updatePaymentOptions();
                    return;
                }
            } else if (paymentType === 'wallet') {
                if (!customerSelect.value) {
                    alert('يجب اختيار عميل لاستخدام الرصيد المحفوظ');
                    document.getElementById('payment-type').value = 'cash';
                    updatePaymentOptions();
                    return;
                }
                walletUsageContainer.style.display = 'block';
            } else if (paymentType === 'mixed') {
                if (!customerSelect.value) {
                    alert('يجب اختيار عميل للدفع المختلط');
                    document.getElementById('payment-type').value = 'cash';
                    updatePaymentOptions();
                    return;
                }
                paidAmountContainer.style.display = 'block';
                walletUsageContainer.style.display = 'block';
            }

            calculateChange();
        }

        // حساب الفكة أو الباقي
        function calculateChange() {
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) -
                         (parseFloat(document.getElementById('discount-amount').value) || 0);
            const paidAmount = parseFloat(document.getElementById('paid-amount').value) || 0;
            const walletUsed = parseFloat(document.getElementById('wallet-used').value) || 0;
            const paymentType = document.getElementById('payment-type').value;

            const totalPaid = paidAmount + walletUsed;
            const difference = totalPaid - total;

            const changeContainer = document.getElementById('change-container');
            const changeLabel = document.getElementById('change-label');
            const changeAmount = document.getElementById('change-amount');

            if (paymentType === 'credit') {
                changeContainer.style.display = 'none';
            } else if (difference > 0) {
                changeLabel.textContent = 'الفكة:';
                changeAmount.textContent = formatCurrency(difference);
                changeAmount.className = 'fw-bold text-success';
                changeContainer.style.display = 'block';
            } else if (difference < 0) {
                changeLabel.textContent = 'الباقي:';
                changeAmount.textContent = formatCurrency(Math.abs(difference));
                changeAmount.className = 'fw-bold text-warning';
                changeContainer.style.display = 'block';
            } else {
                changeContainer.style.display = 'none';
            }
        }

        // مسح السلة
        function clearCart() {
            if (cart.length > 0 && confirm('هل أنت متأكد من مسح السلة؟')) {
                cart = [];
                updateCartDisplay();
                updateTotals();
            }
        }

        // إتمام البيع
        function completeSale() {
            if (cart.length === 0) {
                alert('السلة فارغة');
                return;
            }

            const customerId = document.getElementById('customer-select').value || null;
            const paymentType = document.getElementById('payment-type').value;
            const paidAmount = parseFloat(document.getElementById('paid-amount').value) || 0;
            const walletUsed = parseFloat(document.getElementById('wallet-used').value) || 0;
            const discountAmount = parseFloat(document.getElementById('discount-amount').value) || 0;

            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const total = subtotal - discountAmount;

            // التحقق من صحة البيانات
            if (paymentType === 'cash' && paidAmount < total) {
                alert('المبلغ المدفوع أقل من الإجمالي');
                return;
            }

            if (paymentType === 'credit' && !customerId) {
                alert('يجب اختيار عميل للبيع الآجل');
                return;
            }

            if ((paymentType === 'wallet' || paymentType === 'mixed') && !customerId) {
                alert('يجب اختيار عميل لاستخدام الرصيد المحفوظ');
                return;
            }

            // إنشاء النموذج وإرساله
            const form = document.createElement('form');
            form.method = 'post';
            form.innerHTML = `
                <input type="hidden" name="action" value="complete_sale">
                <input type="hidden" name="customer_id" value="${customerId || ''}">
                <input type="hidden" name="cart_items" value='${JSON.stringify(cart)}'>
                <input type="hidden" name="total_amount" value="${subtotal}">
                <input type="hidden" name="paid_amount" value="${paidAmount}">
                <input type="hidden" name="wallet_used" value="${walletUsed}">
                <input type="hidden" name="payment_type" value="${paymentType}">
                <input type="hidden" name="discount_amount" value="${discountAmount}">
            `;

            document.body.appendChild(form);
            form.submit();
        }

        // تهيئة قارئ الباركود
        function initializeBarcodeScanner() {
            document.addEventListener('keypress', function(e) {
                if (!barcodeMode) return;

                // تجاهل الإدخال في حقول النص
                if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                    return;
                }

                clearTimeout(barcodeTimeout);

                if (e.key === 'Enter') {
                    if (barcodeBuffer.length > 0) {
                        searchProductByBarcode(barcodeBuffer);
                        barcodeBuffer = '';
                    }
                } else {
                    barcodeBuffer += e.key;

                    barcodeTimeout = setTimeout(() => {
                        barcodeBuffer = '';
                    }, 1000);
                }
            });
        }

        // البحث عن منتج بالباركود
        function searchProductByBarcode(barcode) {
            const product = products.find(p => p.barcode === barcode);

            if (product) {
                if (product.stock_quantity > 0) {
                    addToCart(product.id, product.name, parseFloat(product.selling_price), product.stock_quantity);
                    showAlert(`تم إضافة ${product.name} للسلة`, 'success');
                } else {
                    showAlert(`المنتج ${product.name} غير متوفر في المخزون`, 'warning');
                }
            } else {
                showAlert('لم يتم العثور على منتج بهذا الباركود', 'warning');
            }
        }

        // تبديل قارئ الباركود
        function toggleBarcodeScanner() {
            barcodeMode = !barcodeMode;
            const indicator = document.getElementById('barcode-indicator');

            if (barcodeMode) {
                indicator.style.display = 'block';
                showAlert('تم تفعيل قارئ الباركود', 'info');
            } else {
                indicator.style.display = 'none';
                showAlert('تم إلغاء تفعيل قارئ الباركود', 'info');
            }
        }

        // إرسال الفاتورة عبر الواتساب
        function sendWhatsApp(saleId) {
            const customerSelect = document.getElementById('customer-select');
            if (!customerSelect.value) {
                alert('يجب اختيار عميل لإرسال الفاتورة عبر الواتساب');
                return;
            }

            const selectedOption = customerSelect.selectedOptions[0];
            const whatsappNumber = selectedOption.dataset.whatsapp;

            if (!whatsappNumber) {
                alert('لا يوجد رقم واتساب مسجل لهذا العميل');
                return;
            }

            // إنشاء رسالة الفاتورة
            const customerName = selectedOption.text.split(' - ')[0];
            const total = document.getElementById('total').textContent;

            const message = `شكراً لتعاملكم معنا

العميل: ${customerName}
رقم الفاتورة: ${saleId}
الإجمالي: ${total}

محل Z
شكراً لثقتكم`;

            const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
        }

        // تنسيق العملة
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-EG', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount) + ' ج.م';
        }

        // عرض تنبيه
        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alert-container') || createAlertContainer();

            const alertElement = document.createElement('div');
            alertElement.className = `alert alert-${type} alert-dismissible fade show`;
            alertElement.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            alertContainer.appendChild(alertElement);

            setTimeout(() => {
                if (alertElement.parentNode) {
                    alertElement.remove();
                }
            }, 3000);
        }

        // إنشاء حاوية التنبيهات
        function createAlertContainer() {
            const container = document.createElement('div');
            container.id = 'alert-container';
            container.style.position = 'fixed';
            container.style.top = '80px';
            container.style.right = '20px';
            container.style.zIndex = '9999';
            container.style.maxWidth = '400px';
            document.body.appendChild(container);
            return container;
        }
    </script>
</body>
</html>
