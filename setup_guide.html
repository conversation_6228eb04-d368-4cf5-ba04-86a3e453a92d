<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل تثبيت نظام إدارة المحل Z</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .setup-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 800px;
        }
        .header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            border-radius: 15px 15px 0 0;
            text-align: center;
        }
        .step {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        .step:last-child {
            border-bottom: none;
        }
        .step-number {
            background: #667eea;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 15px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            direction: ltr;
            text-align: left;
        }
        .alert-custom {
            border-radius: 10px;
            border: none;
            padding: 15px;
            margin: 15px 0;
        }
        .alert-info-custom {
            background: linear-gradient(45deg, #17a2b8, #20c997);
            color: white;
        }
        .alert-warning-custom {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            color: white;
        }
        .alert-success-custom {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="setup-container">
            <div class="header">
                <h1><i class="fas fa-store"></i> نظام إدارة المحل Z</h1>
                <p class="mb-0">دليل التثبيت والإعداد</p>
            </div>
            
            <div class="alert-warning-custom alert-custom">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>مشكلة في تشغيل PHP:</strong> يبدو أن هناك مشكلة في إعدادات PHP في WAMP. يرجى اتباع الخطوات التالية لحل المشكلة.
            </div>
            
            <div class="step">
                <h4><span class="step-number">1</span>التحقق من حالة WAMP</h4>
                <p>تأكد من أن WAMP يعمل بشكل صحيح:</p>
                <ul>
                    <li>تأكد من أن أيقونة WAMP في شريط المهام خضراء اللون</li>
                    <li>إذا كانت حمراء أو برتقالية، انقر عليها واختر "Restart All Services"</li>
                    <li>تأكد من أن Apache و MySQL يعملان</li>
                </ul>
            </div>
            
            <div class="step">
                <h4><span class="step-number">2</span>إنشاء قاعدة البيانات يدوياً</h4>
                <p>افتح phpMyAdmin من خلال:</p>
                <ul>
                    <li>انقر على أيقونة WAMP → phpMyAdmin</li>
                    <li>أو اذهب إلى: <a href="http://localhost/phpmyadmin" target="_blank">http://localhost/phpmyadmin</a></li>
                </ul>
                
                <p>ثم قم بإنشاء قاعدة بيانات جديدة:</p>
                <ol>
                    <li>انقر على "New" في الجانب الأيسر</li>
                    <li>اكتب اسم قاعدة البيانات: <code>store_z</code></li>
                    <li>اختر Collation: <code>utf8mb4_unicode_ci</code></li>
                    <li>انقر "Create"</li>
                </ol>
            </div>
            
            <div class="step">
                <h4><span class="step-number">3</span>استيراد هيكل قاعدة البيانات</h4>
                <p>بعد إنشاء قاعدة البيانات، قم باستيراد الجداول:</p>
                <ol>
                    <li>انقر على قاعدة البيانات <code>store_z</code></li>
                    <li>انقر على تبويب "Import"</li>
                    <li>انقر "Choose File" واختر ملف <code>database.sql</code> من مجلد النظام</li>
                    <li>انقر "Go" لاستيراد الجداول</li>
                </ol>
            </div>
            
            <div class="step">
                <h4><span class="step-number">4</span>إنشاء مستخدم افتراضي</h4>
                <p>في phpMyAdmin، انقر على جدول <code>users</code> ثم "Insert" وأدخل البيانات التالية:</p>
                <div class="code-block">
                    username: admin<br>
                    password: $2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi<br>
                    full_name: المدير<br>
                    role: admin<br>
                    is_active: 1
                </div>
                <p><small class="text-muted">كلمة المرور المشفرة أعلاه تعادل: <strong>password</strong></small></p>
            </div>
            
            <div class="step">
                <h4><span class="step-number">5</span>إصلاح مشكلة PHP</h4>
                <p>لحل مشكلة 500 Internal Server Error:</p>
                
                <div class="alert-info-custom alert-custom">
                    <strong>الحل الأول:</strong> إعادة تشغيل خدمات WAMP
                </div>
                <ol>
                    <li>انقر على أيقونة WAMP</li>
                    <li>اختر "Restart All Services"</li>
                    <li>انتظر حتى تصبح الأيقونة خضراء</li>
                </ol>
                
                <div class="alert-info-custom alert-custom">
                    <strong>الحل الثاني:</strong> التحقق من إعدادات PHP
                </div>
                <ol>
                    <li>انقر على أيقونة WAMP → PHP → PHP Settings</li>
                    <li>تأكد من تفعيل: display_errors, mysqli, gd</li>
                    <li>أعد تشغيل Apache</li>
                </ol>
                
                <div class="alert-info-custom alert-custom">
                    <strong>الحل الثالث:</strong> التحقق من ملف .htaccess
                </div>
                <p>تأكد من عدم وجود ملف .htaccess في مجلد النظام، أو قم بحذفه مؤقتاً</p>
            </div>
            
            <div class="step">
                <h4><span class="step-number">6</span>اختبار النظام</h4>
                <p>بعد حل مشكلة PHP، جرب الوصول إلى:</p>
                <ul>
                    <li><a href="http://localhost/z/login.php" target="_blank">صفحة تسجيل الدخول</a></li>
                    <li><a href="http://localhost/z/" target="_blank">الصفحة الرئيسية</a></li>
                </ul>
                
                <div class="alert-success-custom alert-custom">
                    <strong>بيانات تسجيل الدخول الافتراضية:</strong><br>
                    اسم المستخدم: <code>admin</code><br>
                    كلمة المرور: <code>password</code>
                </div>
            </div>
            
            <div class="step">
                <h4><span class="step-number">7</span>ميزات النظام</h4>
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-check text-success"></i> إدارة المنتجات</h6>
                        <h6><i class="fas fa-check text-success"></i> إدارة العملاء</h6>
                        <h6><i class="fas fa-check text-success"></i> إدارة الموردين</h6>
                        <h6><i class="fas fa-check text-success"></i> نظام نقطة البيع</h6>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-check text-success"></i> إدارة المشتريات</h6>
                        <h6><i class="fas fa-check text-success"></i> التقارير والإحصائيات</h6>
                        <h6><i class="fas fa-check text-success"></i> نظام الباركود</h6>
                        <h6><i class="fas fa-check text-success"></i> إرسال فواتير واتساب</h6>
                    </div>
                </div>
            </div>
            
            <div class="step text-center">
                <h5><i class="fas fa-heart text-danger"></i> نظام إدارة المحل Z جاهز للاستخدام!</h5>
                <p class="text-muted">تم تطوير النظام بعناية ليلبي جميع احتياجات إدارة المحلات التجارية</p>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
