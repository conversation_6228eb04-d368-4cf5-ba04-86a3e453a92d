<?php
/**
 * نظام إدارة المحل Z - البحث السريع
 */

require_once '../config.php';
require_login();

header('Content-Type: application/json');

$query = $_GET['q'] ?? '';
$results = [];

if (strlen($query) >= 2) {
    try {
        // البحث في المنتجات
        $stmt = $pdo->prepare("
            SELECT id, name, 'product' as type 
            FROM products 
            WHERE (name LIKE ? OR barcode LIKE ?) AND is_active = 1 
            LIMIT 5
        ");
        $stmt->execute(["%$query%", "%$query%"]);
        $results = array_merge($results, $stmt->fetchAll());
        
        // البحث في العملاء
        $stmt = $pdo->prepare("
            SELECT id, name, 'customer' as type 
            FROM customers 
            WHERE (name LIKE ? OR phone LIKE ?) AND is_active = 1 
            LIMIT 5
        ");
        $stmt->execute(["%$query%", "%$query%"]);
        $results = array_merge($results, $stmt->fetchAll());
        
        // البحث في الموردين
        $stmt = $pdo->prepare("
            SELECT id, name, 'supplier' as type 
            FROM suppliers 
            WHERE name LIKE ? AND is_active = 1 
            LIMIT 5
        ");
        $stmt->execute(["%$query%"]);
        $results = array_merge($results, $stmt->fetchAll());
        
        // البحث في المبيعات
        $stmt = $pdo->prepare("
            SELECT id, invoice_number as name, 'sale' as type 
            FROM sales 
            WHERE invoice_number LIKE ? 
            LIMIT 3
        ");
        $stmt->execute(["%$query%"]);
        $results = array_merge($results, $stmt->fetchAll());
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['error' => 'خطأ في البحث']);
        exit;
    }
}

echo json_encode($results);
?>
