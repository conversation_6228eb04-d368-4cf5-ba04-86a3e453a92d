<?php
/**
 * نظام إدارة المحل Z - إنتاج الباركود
 */

require_once 'config.php';
require_login();

$message = '';
$error = '';
$barcode_generated = false;
$barcode_data = null;

// معالجة إنتاج الباركود
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action']) && $_POST['action'] === 'generate_single') {
            $product_id = $_POST['product_id'];
            $barcode_type = $_POST['barcode_type'] ?? 'CODE128';
            
            if (empty($product_id)) {
                throw new Exception('يرجى اختيار منتج');
            }
            
            // جلب بيانات المنتج
            $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ? AND is_active = 1");
            $stmt->execute([$product_id]);
            $product = $stmt->fetch();
            
            if (!$product) {
                throw new Exception('المنتج غير موجود');
            }
            
            // إنشاء باركود إذا لم يكن موجوداً
            if (empty($product['barcode'])) {
                $new_barcode = generateBarcode();
                $stmt = $pdo->prepare("UPDATE products SET barcode = ? WHERE id = ?");
                $stmt->execute([$new_barcode, $product_id]);
                $product['barcode'] = $new_barcode;
            }
            
            $barcode_data = [
                'product' => $product,
                'barcode_type' => $barcode_type
            ];
            $barcode_generated = true;
            $message = 'تم إنتاج الباركود بنجاح';
            
        } elseif (isset($_POST['action']) && $_POST['action'] === 'generate_batch') {
            $category_id = $_POST['category_id'] ?? '';
            $barcode_type = $_POST['barcode_type'] ?? 'CODE128';
            
            // جلب المنتجات
            $sql = "SELECT * FROM products WHERE is_active = 1";
            $params = [];
            
            if ($category_id) {
                $sql .= " AND category_id = ?";
                $params[] = $category_id;
            }
            
            $sql .= " ORDER BY name";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            $products = $stmt->fetchAll();
            
            $updated_count = 0;
            foreach ($products as $product) {
                if (empty($product['barcode'])) {
                    $new_barcode = generateBarcode();
                    $stmt = $pdo->prepare("UPDATE products SET barcode = ? WHERE id = ?");
                    $stmt->execute([$new_barcode, $product['id']]);
                    $updated_count++;
                }
            }
            
            $message = "تم إنتاج باركود لـ $updated_count منتج";
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// دالة إنتاج باركود فريد
function generateBarcode() {
    global $pdo;
    
    do {
        $barcode = '2' . str_pad(rand(1, 999999999999), 12, '0', STR_PAD_LEFT);
        
        // التحقق من عدم وجود الباركود
        $stmt = $pdo->prepare("SELECT id FROM products WHERE barcode = ?");
        $stmt->execute([$barcode]);
        $exists = $stmt->fetch();
        
    } while ($exists);
    
    return $barcode;
}

// جلب المنتجات
$stmt = $pdo->prepare("SELECT id, name, barcode FROM products WHERE is_active = 1 ORDER BY name");
$stmt->execute();
$products = $stmt->fetchAll();

// جلب الفئات
$stmt = $pdo->prepare("SELECT * FROM categories WHERE is_active = 1 ORDER BY name");
$stmt->execute();
$categories = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنتاج الباركود - نظام إدارة المحل Z</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إنتاج الباركود</h1>
                </div>
                
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <div class="row">
                    <!-- إنتاج باركود منتج واحد -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">إنتاج باركود لمنتج واحد</h5>
                            </div>
                            <div class="card-body">
                                <form method="post">
                                    <input type="hidden" name="action" value="generate_single">
                                    
                                    <div class="mb-3">
                                        <label class="form-label">اختر المنتج</label>
                                        <select class="form-select" name="product_id" required>
                                            <option value="">اختر منتج...</option>
                                            <?php foreach ($products as $product): ?>
                                                <option value="<?= $product['id'] ?>">
                                                    <?= htmlspecialchars($product['name']) ?>
                                                    <?php if ($product['barcode']): ?>
                                                        (<?= htmlspecialchars($product['barcode']) ?>)
                                                    <?php else: ?>
                                                        (بدون باركود)
                                                    <?php endif; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">نوع الباركود</label>
                                        <select class="form-select" name="barcode_type">
                                            <option value="CODE128">CODE128</option>
                                            <option value="CODE39">CODE39</option>
                                            <option value="EAN13">EAN13</option>
                                            <option value="EAN8">EAN8</option>
                                        </select>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-barcode"></i> إنتاج الباركود
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- إنتاج باركود مجموعة -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">إنتاج باركود لمجموعة منتجات</h5>
                            </div>
                            <div class="card-body">
                                <form method="post">
                                    <input type="hidden" name="action" value="generate_batch">
                                    
                                    <div class="mb-3">
                                        <label class="form-label">الفئة (اختياري)</label>
                                        <select class="form-select" name="category_id">
                                            <option value="">جميع الفئات</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?= $category['id'] ?>">
                                                    <?= htmlspecialchars($category['name']) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="form-text">سيتم إنتاج باركود للمنتجات التي لا تحتوي على باركود فقط</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">نوع الباركود</label>
                                        <select class="form-select" name="barcode_type">
                                            <option value="CODE128">CODE128</option>
                                            <option value="CODE39">CODE39</option>
                                            <option value="EAN13">EAN13</option>
                                            <option value="EAN8">EAN8</option>
                                        </select>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-barcode"></i> إنتاج باركود للمجموعة
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <?php if ($barcode_generated && $barcode_data): ?>
                    <!-- عرض الباركود المُنتج -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">الباركود المُنتج</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>معلومات المنتج:</h6>
                                    <p><strong>الاسم:</strong> <?= htmlspecialchars($barcode_data['product']['name']) ?></p>
                                    <p><strong>الباركود:</strong> <?= htmlspecialchars($barcode_data['product']['barcode']) ?></p>
                                    <p><strong>السعر:</strong> <?= number_format($barcode_data['product']['selling_price'], 2) ?> ج.م</p>
                                </div>
                                <div class="col-md-6 text-center">
                                    <div class="barcode-container">
                                        <canvas id="barcode-canvas"></canvas>
                                        <div class="mt-2">
                                            <button type="button" class="btn btn-success" onclick="printBarcode()">
                                                <i class="fas fa-print"></i> طباعة
                                            </button>
                                            <button type="button" class="btn btn-info" onclick="downloadBarcode()">
                                                <i class="fas fa-download"></i> تحميل
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- قائمة المنتجات وباركودها -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">قائمة المنتجات والباركود</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم المنتج</th>
                                        <th>الباركود</th>
                                        <th>السعر</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($products as $product): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($product['name']) ?></td>
                                            <td>
                                                <?php if ($product['barcode']): ?>
                                                    <code><?= htmlspecialchars($product['barcode']) ?></code>
                                                <?php else: ?>
                                                    <span class="text-muted">بدون باركود</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= number_format($product['selling_price'] ?? 0, 2) ?> ج.م</td>
                                            <td>
                                                <?php if ($product['barcode']): ?>
                                                    <button type="button" class="btn btn-sm btn-primary" 
                                                            onclick="generateSingleBarcode('<?= htmlspecialchars($product['barcode']) ?>', '<?= htmlspecialchars($product['name']) ?>')">
                                                        <i class="fas fa-eye"></i> عرض
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-success" 
                                                            onclick="printSingleBarcode('<?= htmlspecialchars($product['barcode']) ?>', '<?= htmlspecialchars($product['name']) ?>')">
                                                        <i class="fas fa-print"></i> طباعة
                                                    </button>
                                                <?php else: ?>
                                                    <form method="post" style="display: inline;">
                                                        <input type="hidden" name="action" value="generate_single">
                                                        <input type="hidden" name="product_id" value="<?= $product['id'] ?>">
                                                        <button type="submit" class="btn btn-sm btn-warning">
                                                            <i class="fas fa-barcode"></i> إنتاج
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- مودال عرض الباركود -->
    <div class="modal fade" id="barcodeModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">عرض الباركود</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <div id="modal-product-name" class="mb-3"></div>
                    <canvas id="modal-barcode-canvas"></canvas>
                    <div id="modal-barcode-text" class="mt-2"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success" onclick="printModalBarcode()">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        <?php if ($barcode_generated && $barcode_data): ?>
            // إنتاج الباركود المعروض
            document.addEventListener('DOMContentLoaded', function() {
                generateBarcodeCanvas(
                    'barcode-canvas',
                    '<?= $barcode_data['product']['barcode'] ?>',
                    '<?= $barcode_data['barcode_type'] ?>'
                );
            });
        <?php endif; ?>
        
        // إنتاج باركود في canvas
        function generateBarcodeCanvas(canvasId, barcodeValue, barcodeType = 'CODE128') {
            try {
                JsBarcode('#' + canvasId, barcodeValue, {
                    format: barcodeType,
                    width: 2,
                    height: 100,
                    displayValue: true,
                    fontSize: 14,
                    margin: 10
                });
            } catch (error) {
                console.error('خطأ في إنتاج الباركود:', error);
                document.getElementById(canvasId).innerHTML = '<p class="text-danger">خطأ في إنتاج الباركود</p>';
            }
        }
        
        // عرض باركود منتج واحد
        function generateSingleBarcode(barcode, productName) {
            document.getElementById('modal-product-name').innerHTML = '<h6>' + productName + '</h6>';
            document.getElementById('modal-barcode-text').innerHTML = '<code>' + barcode + '</code>';
            
            generateBarcodeCanvas('modal-barcode-canvas', barcode);
            
            new bootstrap.Modal(document.getElementById('barcodeModal')).show();
        }
        
        // طباعة الباركود الرئيسي
        function printBarcode() {
            const canvas = document.getElementById('barcode-canvas');
            if (canvas) {
                printCanvas(canvas, '<?= htmlspecialchars($barcode_data['product']['name'] ?? '') ?>');
            }
        }
        
        // طباعة باركود من المودال
        function printModalBarcode() {
            const canvas = document.getElementById('modal-barcode-canvas');
            const productName = document.getElementById('modal-product-name').textContent;
            if (canvas) {
                printCanvas(canvas, productName);
            }
        }
        
        // طباعة باركود منتج واحد مباشرة
        function printSingleBarcode(barcode, productName) {
            const tempCanvas = document.createElement('canvas');
            generateBarcodeCanvas(tempCanvas.id = 'temp-canvas', barcode);
            document.body.appendChild(tempCanvas);
            
            setTimeout(() => {
                printCanvas(tempCanvas, productName);
                document.body.removeChild(tempCanvas);
            }, 100);
        }
        
        // طباعة canvas
        function printCanvas(canvas, productName) {
            const printWindow = window.open('', '_blank');
            const dataURL = canvas.toDataURL();
            
            printWindow.document.write(`
                <html>
                    <head>
                        <title>طباعة باركود - ${productName}</title>
                        <style>
                            body {
                                text-align: center;
                                margin: 20px;
                                font-family: Arial, sans-serif;
                            }
                            .product-name {
                                font-size: 16px;
                                font-weight: bold;
                                margin-bottom: 10px;
                            }
                            .barcode-image {
                                max-width: 100%;
                                height: auto;
                            }
                            @media print {
                                body { margin: 0; }
                            }
                        </style>
                    </head>
                    <body>
                        <div class="product-name">${productName}</div>
                        <img src="${dataURL}" class="barcode-image" alt="Barcode">
                        <script>
                            window.onload = function() {
                                window.print();
                                window.close();
                            };
                        </script>
                    </body>
                </html>
            `);
            printWindow.document.close();
        }
        
        // تحميل الباركود
        function downloadBarcode() {
            const canvas = document.getElementById('barcode-canvas');
            if (canvas) {
                const link = document.createElement('a');
                link.download = 'barcode-<?= $barcode_data['product']['barcode'] ?? 'unknown' ?>.png';
                link.href = canvas.toDataURL();
                link.click();
            }
        }
    </script>
</body>
</html>
