<?php
/**
 * نظام إدارة المحل Z - إدارة العملاء
 */

require_once 'config.php';
require_login();

$action = $_GET['action'] ?? 'list';
$customer_id = $_GET['id'] ?? null;
$message = '';
$error = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if ($action === 'add' || $action === 'edit') {
            $name = trim($_POST['name']);
            $phone = trim($_POST['phone']);
            $whatsapp = trim($_POST['whatsapp']);
            $email = trim($_POST['email']);
            $address = trim($_POST['address']);
            
            // التحقق من البيانات المطلوبة
            if (empty($name)) {
                throw new Exception('يرجى إدخال اسم العميل');
            }
            
            // التحقق من عدم تكرار رقم الهاتف
            if (!empty($phone)) {
                $stmt = $pdo->prepare("SELECT id FROM customers WHERE phone = ? AND id != ?");
                $stmt->execute([$phone, $customer_id ?: 0]);
                if ($stmt->fetch()) {
                    throw new Exception('رقم الهاتف مستخدم بالفعل');
                }
            }
            
            if ($action === 'add') {
                // إضافة عميل جديد
                $stmt = $pdo->prepare("
                    INSERT INTO customers (name, phone, whatsapp, email, address) 
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([$name, $phone, $whatsapp, $email, $address]);
                
                $message = 'تم إضافة العميل بنجاح';
                
            } else {
                // تعديل عميل موجود
                $stmt = $pdo->prepare("
                    UPDATE customers SET name = ?, phone = ?, whatsapp = ?, email = ?, address = ? 
                    WHERE id = ?
                ");
                $stmt->execute([$name, $phone, $whatsapp, $email, $address, $customer_id]);
                
                $message = 'تم تحديث بيانات العميل بنجاح';
            }
            
        } elseif ($action === 'delete') {
            $customer_id = $_POST['customer_id'];
            
            // التحقق من عدم وجود مبيعات للعميل
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM sales WHERE customer_id = ?");
            $stmt->execute([$customer_id]);
            $has_sales = $stmt->fetchColumn();
            
            if ($has_sales > 0) {
                // إلغاء تفعيل العميل بدلاً من حذفه
                $stmt = $pdo->prepare("UPDATE customers SET is_active = 0 WHERE id = ?");
                $stmt->execute([$customer_id]);
                $message = 'تم إلغاء تفعيل العميل';
            } else {
                // حذف العميل نهائياً
                $stmt = $pdo->prepare("DELETE FROM customers WHERE id = ?");
                $stmt->execute([$customer_id]);
                $message = 'تم حذف العميل';
            }
            
        } elseif ($action === 'adjust_wallet') {
            $customer_id = $_POST['customer_id'];
            $adjustment_type = $_POST['adjustment_type']; // add or subtract
            $amount = floatval($_POST['amount']);
            $notes = trim($_POST['notes']);
            
            if ($amount <= 0) {
                throw new Exception('يرجى إدخال مبلغ صحيح');
            }
            
            // الحصول على الرصيد الحالي
            $stmt = $pdo->prepare("SELECT wallet_balance FROM customers WHERE id = ?");
            $stmt->execute([$customer_id]);
            $current_balance = $stmt->fetchColumn();
            
            if ($current_balance !== false) {
                $new_balance = $adjustment_type === 'add' ? 
                    $current_balance + $amount : 
                    $current_balance - $amount;
                
                // التأكد من عدم وجود رصيد سالب
                if ($new_balance < 0) {
                    throw new Exception('لا يمكن أن يكون الرصيد سالباً');
                }
                
                // تحديث الرصيد
                $stmt = $pdo->prepare("UPDATE customers SET wallet_balance = ? WHERE id = ?");
                $stmt->execute([$new_balance, $customer_id]);
                
                $message = 'تم تعديل رصيد العميل بنجاح';
            }
            
        } elseif ($action === 'pay_debt') {
            $customer_id = $_POST['customer_id'];
            $payment_amount = floatval($_POST['payment_amount']);
            $use_wallet = isset($_POST['use_wallet']);
            
            if ($payment_amount <= 0) {
                throw new Exception('يرجى إدخال مبلغ صحيح');
            }
            
            // الحصول على بيانات العميل
            $stmt = $pdo->prepare("SELECT debt_balance, wallet_balance FROM customers WHERE id = ?");
            $stmt->execute([$customer_id]);
            $customer_data = $stmt->fetch();
            
            if ($customer_data) {
                $debt_balance = $customer_data['debt_balance'];
                $wallet_balance = $customer_data['wallet_balance'];
                
                if ($payment_amount > $debt_balance) {
                    throw new Exception('المبلغ المدفوع أكبر من الدين المستحق');
                }
                
                $total_payment = $payment_amount;
                $wallet_used = 0;
                
                // استخدام الرصيد المحفوظ إذا طُلب ذلك
                if ($use_wallet && $wallet_balance > 0) {
                    $wallet_used = min($wallet_balance, $payment_amount);
                    $total_payment = $payment_amount - $wallet_used;
                    $wallet_balance -= $wallet_used;
                }
                
                $new_debt = $debt_balance - $payment_amount;
                
                // تحديث بيانات العميل
                $stmt = $pdo->prepare("
                    UPDATE customers 
                    SET debt_balance = ?, wallet_balance = ? 
                    WHERE id = ?
                ");
                $stmt->execute([$new_debt, $wallet_balance, $customer_id]);
                
                // تسجيل الدفعة في الخزنة (إذا كان هناك دفع نقدي)
                if ($total_payment > 0) {
                    $stmt = $pdo->prepare("SELECT balance_after FROM cash_register ORDER BY id DESC LIMIT 1");
                    $stmt->execute();
                    $current_balance = $stmt->fetchColumn() ?: 0;
                    
                    $stmt = $pdo->prepare("
                        INSERT INTO cash_register (transaction_type, amount, balance_before, balance_after, 
                                                 reference_type, description, created_by) 
                        VALUES ('income', ?, ?, ?, 'manual', ?, ?)
                    ");
                    $stmt->execute([
                        $total_payment, 
                        $current_balance, 
                        $current_balance + $total_payment,
                        "سداد دين من العميل",
                        $_SESSION['user_id']
                    ]);
                }
                
                $message = 'تم تسجيل الدفعة بنجاح';
            }
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// جلب البيانات للعرض
if ($action === 'edit' && $customer_id) {
    $stmt = $pdo->prepare("SELECT * FROM customers WHERE id = ?");
    $stmt->execute([$customer_id]);
    $customer = $stmt->fetch();
    
    if (!$customer) {
        $error = 'العميل غير موجود';
        $action = 'list';
    }
}

// جلب العملاء للعرض
if ($action === 'list') {
    $search = $_GET['search'] ?? '';
    $filter = $_GET['filter'] ?? '';
    
    $sql = "SELECT * FROM customers WHERE is_active = 1";
    $params = [];
    
    if ($search) {
        $sql .= " AND (name LIKE ? OR phone LIKE ? OR whatsapp LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if ($filter === 'debts') {
        $sql .= " AND debt_balance > 0";
    } elseif ($filter === 'wallet') {
        $sql .= " AND wallet_balance > 0";
    }
    
    $sql .= " ORDER BY name";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $customers = $stmt->fetchAll();
}

// جلب إحصائيات العملاء
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM customers WHERE is_active = 1");
    $stmt->execute();
    $total_customers = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COALESCE(SUM(debt_balance), 0) FROM customers WHERE debt_balance > 0");
    $stmt->execute();
    $total_debts = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COALESCE(SUM(wallet_balance), 0) FROM customers WHERE wallet_balance > 0");
    $stmt->execute();
    $total_wallet = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM customers WHERE debt_balance > 0");
    $stmt->execute();
    $customers_with_debts = $stmt->fetchColumn();
    
} catch (PDOException $e) {
    $total_customers = $total_debts = $total_wallet = $customers_with_debts = 0;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء - نظام إدارة المحل Z</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <?php if ($action === 'add'): ?>
                            إضافة عميل جديد
                        <?php elseif ($action === 'edit'): ?>
                            تعديل بيانات العميل
                        <?php else: ?>
                            إدارة العملاء
                        <?php endif; ?>
                    </h1>
                    
                    <?php if ($action === 'list'): ?>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="customers.php?action=add" class="btn btn-sm btn-primary">
                                <i class="fas fa-plus"></i> إضافة عميل
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
                
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($action === 'list'): ?>
                    <!-- إحصائيات العملاء -->
                    <div class="row mb-4">
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                إجمالي العملاء
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($total_customers) ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-users fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                إجمالي الديون
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($total_debts, 2) ?> ج.م
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                الأرصدة المحفوظة
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($total_wallet, 2) ?> ج.م
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-wallet fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-info shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                عملاء مدينون
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($customers_with_debts) ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-user-clock fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if ($action === 'add' || $action === 'edit'): ?>
                    <!-- نموذج إضافة/تعديل العميل -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <?= $action === 'add' ? 'إضافة عميل جديد' : 'تعديل بيانات العميل' ?>
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="post" class="needs-validation" novalidate>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">اسم العميل *</label>
                                            <input type="text" class="form-control" name="name" 
                                                   value="<?= htmlspecialchars($customer['name'] ?? '') ?>" required>
                                            <div class="invalid-feedback">يرجى إدخال اسم العميل</div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم الهاتف</label>
                                            <input type="tel" class="form-control" name="phone" 
                                                   value="<?= htmlspecialchars($customer['phone'] ?? '') ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم الواتساب</label>
                                            <input type="tel" class="form-control" name="whatsapp" 
                                                   value="<?= htmlspecialchars($customer['whatsapp'] ?? '') ?>"
                                                   placeholder="مثال: 201234567890">
                                            <div class="form-text">أدخل الرقم بالصيغة الدولية بدون علامة +</div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">البريد الإلكتروني</label>
                                            <input type="email" class="form-control" name="email" 
                                                   value="<?= htmlspecialchars($customer['email'] ?? '') ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="address" rows="3"><?= htmlspecialchars($customer['address'] ?? '') ?></textarea>
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i>
                                        <?= $action === 'add' ? 'إضافة العميل' : 'حفظ التغييرات' ?>
                                    </button>
                                    <a href="customers.php" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> إلغاء
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                <?php else: ?>
                    <!-- قائمة العملاء -->
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h5 class="mb-0">قائمة العملاء</h5>
                                </div>
                                <div class="col-md-6">
                                    <form method="get" class="d-flex gap-2">
                                        <input type="text" class="form-control form-control-sm" name="search" 
                                               placeholder="البحث..." value="<?= htmlspecialchars($_GET['search'] ?? '') ?>">
                                        <select class="form-select form-select-sm" name="filter">
                                            <option value="">جميع العملاء</option>
                                            <option value="debts" <?= ($_GET['filter'] ?? '') === 'debts' ? 'selected' : '' ?>>العملاء المدينون</option>
                                            <option value="wallet" <?= ($_GET['filter'] ?? '') === 'wallet' ? 'selected' : '' ?>>لديهم رصيد محفوظ</option>
                                        </select>
                                        <button type="submit" class="btn btn-sm btn-primary">بحث</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>اسم العميل</th>
                                            <th>رقم الهاتف</th>
                                            <th>الواتساب</th>
                                            <th>الرصيد المحفوظ</th>
                                            <th>الديون</th>
                                            <th>إجمالي المشتريات</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (empty($customers)): ?>
                                            <tr>
                                                <td colspan="7" class="text-center text-muted py-4">
                                                    لا توجد عملاء
                                                </td>
                                            </tr>
                                        <?php else: ?>
                                            <?php foreach ($customers as $customer): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?= htmlspecialchars($customer['name']) ?></strong>
                                                        <?php if ($customer['email']): ?>
                                                            <br><small class="text-muted"><?= htmlspecialchars($customer['email']) ?></small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?= htmlspecialchars($customer['phone']) ?></td>
                                                    <td>
                                                        <?php if ($customer['whatsapp']): ?>
                                                            <a href="https://wa.me/<?= htmlspecialchars($customer['whatsapp']) ?>" 
                                                               target="_blank" class="text-success">
                                                                <i class="fab fa-whatsapp"></i> <?= htmlspecialchars($customer['whatsapp']) ?>
                                                            </a>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge <?= $customer['wallet_balance'] > 0 ? 'bg-success' : 'bg-secondary' ?>">
                                                            <?= number_format($customer['wallet_balance'], 2) ?> ج.م
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge <?= $customer['debt_balance'] > 0 ? 'bg-warning' : 'bg-secondary' ?>">
                                                            <?= number_format($customer['debt_balance'], 2) ?> ج.م
                                                        </span>
                                                    </td>
                                                    <td><?= number_format($customer['total_purchases'], 2) ?> ج.م</td>
                                                    <td>
                                                        <div class="action-buttons">
                                                            <a href="customers.php?action=edit&id=<?= $customer['id'] ?>" 
                                                               class="btn btn-sm btn-primary" title="تعديل">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <?php if ($customer['wallet_balance'] > 0 || $customer['debt_balance'] > 0): ?>
                                                                <button type="button" class="btn btn-sm btn-info" 
                                                                        onclick="manageCustomerFinance(<?= $customer['id'] ?>, '<?= htmlspecialchars($customer['name']) ?>', <?= $customer['wallet_balance'] ?>, <?= $customer['debt_balance'] ?>)" 
                                                                        title="إدارة الرصيد والديون">
                                                                    <i class="fas fa-wallet"></i>
                                                                </button>
                                                            <?php endif; ?>
                                                            <button type="button" class="btn btn-sm btn-danger" 
                                                                    onclick="deleteCustomer(<?= $customer['id'] ?>, '<?= htmlspecialchars($customer['name']) ?>')" 
                                                                    title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>
    
    <!-- مودال إدارة الرصيد والديون -->
    <div class="modal fade" id="financeModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إدارة الرصيد والديون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>تعديل الرصيد المحفوظ</h6>
                            <form method="post" id="walletForm">
                                <input type="hidden" name="action" value="adjust_wallet">
                                <input type="hidden" name="customer_id" id="finance_customer_id">
                                
                                <div class="mb-3">
                                    <label class="form-label">العميل</label>
                                    <input type="text" class="form-control" id="finance_customer_name" readonly>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">الرصيد الحالي</label>
                                    <input type="text" class="form-control" id="finance_current_wallet" readonly>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">نوع التعديل</label>
                                    <select class="form-select" name="adjustment_type" required>
                                        <option value="add">إضافة رصيد</option>
                                        <option value="subtract">خصم رصيد</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">المبلغ</label>
                                    <input type="number" class="form-control" name="amount" step="0.01" min="0.01" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="2"></textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">حفظ التعديل</button>
                            </form>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>سداد الديون</h6>
                            <form method="post" id="debtForm">
                                <input type="hidden" name="action" value="pay_debt">
                                <input type="hidden" name="customer_id" id="debt_customer_id">
                                
                                <div class="mb-3">
                                    <label class="form-label">الدين الحالي</label>
                                    <input type="text" class="form-control" id="finance_current_debt" readonly>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">مبلغ السداد</label>
                                    <input type="number" class="form-control" name="payment_amount" step="0.01" min="0.01" required>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="use_wallet" id="use_wallet">
                                        <label class="form-check-label" for="use_wallet">
                                            استخدام الرصيد المحفوظ
                                        </label>
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-success">تسجيل السداد</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        function manageCustomerFinance(customerId, customerName, walletBalance, debtBalance) {
            document.getElementById('finance_customer_id').value = customerId;
            document.getElementById('debt_customer_id').value = customerId;
            document.getElementById('finance_customer_name').value = customerName;
            document.getElementById('finance_current_wallet').value = walletBalance.toFixed(2) + ' ج.م';
            document.getElementById('finance_current_debt').value = debtBalance.toFixed(2) + ' ج.م';
            
            new bootstrap.Modal(document.getElementById('financeModal')).show();
        }
        
        function deleteCustomer(customerId, customerName) {
            if (confirm(`هل أنت متأكد من حذف العميل "${customerName}"؟`)) {
                const form = document.createElement('form');
                form.method = 'post';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="customer_id" value="${customerId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        // تفعيل التحقق من صحة النماذج
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
</body>
</html>
