<?php
/**
 * نظام إدارة المحل Z - ملف التثبيت
 * تاريخ الإنشاء: 2025-07-26
 */

session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// التحقق من وجود ملف الإعدادات
if (file_exists('config.php')) {
    header('Location: index.php');
    exit();
}

$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$error = '';
$success = '';

// معالجة خطوات التثبيت
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 1:
            // التحقق من متطلبات النظام
            $requirements_met = true;
            
            if (version_compare(PHP_VERSION, '7.4.0', '<')) {
                $error = 'يتطلب النظام PHP 7.4 أو أحدث';
                $requirements_met = false;
            }
            
            if (!extension_loaded('mysqli')) {
                $error = 'يتطلب النظام تفعيل MySQLi extension';
                $requirements_met = false;
            }
            
            if (!extension_loaded('gd')) {
                $error = 'يتطلب النظام تفعيل GD extension لإنتاج الباركود';
                $requirements_met = false;
            }
            
            if ($requirements_met) {
                $step = 2;
            }
            break;
            
        case 2:
            // إعداد قاعدة البيانات
            $db_host = $_POST['db_host'] ?? 'localhost';
            $db_name = $_POST['db_name'] ?? 'z_store';
            $db_user = $_POST['db_user'] ?? 'root';
            $db_pass = $_POST['db_pass'] ?? '';
            
            try {
                // الاتصال بقاعدة البيانات
                $mysqli = new mysqli($db_host, $db_user, $db_pass);
                
                if ($mysqli->connect_error) {
                    throw new Exception('فشل الاتصال بقاعدة البيانات: ' . $mysqli->connect_error);
                }
                
                // إنشاء قاعدة البيانات
                $mysqli->query("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $mysqli->select_db($db_name);
                
                // تنفيذ ملف SQL
                $sql_content = file_get_contents('database.sql');
                $sql_content = str_replace('z_store', $db_name, $sql_content);
                
                // تقسيم الاستعلامات
                $queries = explode(';', $sql_content);
                
                foreach ($queries as $query) {
                    $query = trim($query);
                    if (!empty($query) && !preg_match('/^(--|\/\*|CREATE DATABASE|USE)/', $query)) {
                        if (!$mysqli->query($query)) {
                            throw new Exception('خطأ في تنفيذ الاستعلام: ' . $mysqli->error);
                        }
                    }
                }
                
                // حفظ إعدادات قاعدة البيانات
                $_SESSION['db_config'] = [
                    'host' => $db_host,
                    'name' => $db_name,
                    'user' => $db_user,
                    'pass' => $db_pass
                ];
                
                $mysqli->close();
                $step = 3;
                $success = 'تم إنشاء قاعدة البيانات بنجاح';
                
            } catch (Exception $e) {
                $error = $e->getMessage();
            }
            break;
            
        case 3:
            // إعداد المتجر
            $store_name = $_POST['store_name'] ?? 'محل Z';
            $store_address = $_POST['store_address'] ?? '';
            $store_phone = $_POST['store_phone'] ?? '';
            $store_email = $_POST['store_email'] ?? '';
            $admin_username = $_POST['admin_username'] ?? 'admin';
            $admin_password = $_POST['admin_password'] ?? '';
            $admin_name = $_POST['admin_name'] ?? 'المدير العام';
            
            if (empty($admin_password)) {
                $error = 'يجب إدخال كلمة مرور المدير';
                break;
            }
            
            try {
                $db_config = $_SESSION['db_config'];
                $mysqli = new mysqli($db_config['host'], $db_config['user'], $db_config['pass'], $db_config['name']);
                
                if ($mysqli->connect_error) {
                    throw new Exception('فشل الاتصال بقاعدة البيانات');
                }
                
                // تحديث إعدادات المتجر
                $stmt = $mysqli->prepare("UPDATE settings SET setting_value = ? WHERE setting_name = ?");
                
                $settings = [
                    'store_name' => $store_name,
                    'store_address' => $store_address,
                    'store_phone' => $store_phone,
                    'store_email' => $store_email
                ];
                
                foreach ($settings as $name => $value) {
                    $stmt->bind_param('ss', $value, $name);
                    $stmt->execute();
                }
                
                // تحديث بيانات المدير
                $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
                $stmt = $mysqli->prepare("UPDATE users SET username = ?, password = ?, full_name = ? WHERE id = 1");
                $stmt->bind_param('sss', $admin_username, $hashed_password, $admin_name);
                $stmt->execute();
                
                $stmt->close();
                $mysqli->close();
                
                // إنشاء ملف الإعدادات
                $config_content = "<?php\n";
                $config_content .= "// إعدادات قاعدة البيانات\n";
                $config_content .= "define('DB_HOST', '{$db_config['host']}');\n";
                $config_content .= "define('DB_NAME', '{$db_config['name']}');\n";
                $config_content .= "define('DB_USER', '{$db_config['user']}');\n";
                $config_content .= "define('DB_PASS', '{$db_config['pass']}');\n\n";
                $config_content .= "// إعدادات عامة\n";
                $config_content .= "define('SITE_URL', 'http://' . \$_SERVER['HTTP_HOST'] . dirname(\$_SERVER['SCRIPT_NAME']));\n";
                $config_content .= "define('UPLOAD_PATH', 'uploads/');\n";
                $config_content .= "define('BACKUP_PATH', 'backups/');\n\n";
                $config_content .= "// إعدادات الجلسة\n";
                $config_content .= "ini_set('session.cookie_lifetime', 86400);\n";
                $config_content .= "session_start();\n\n";
                $config_content .= "// اتصال قاعدة البيانات\n";
                $config_content .= "try {\n";
                $config_content .= "    \$pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);\n";
                $config_content .= "    \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);\n";
                $config_content .= "    \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);\n";
                $config_content .= "} catch (PDOException \$e) {\n";
                $config_content .= "    die('خطأ في الاتصال بقاعدة البيانات: ' . \$e->getMessage());\n";
                $config_content .= "}\n\n";
                $config_content .= "// دوال مساعدة\n";
                $config_content .= "function redirect(\$url) {\n";
                $config_content .= "    header('Location: ' . \$url);\n";
                $config_content .= "    exit();\n";
                $config_content .= "}\n\n";
                $config_content .= "function is_logged_in() {\n";
                $config_content .= "    return isset(\$_SESSION['user_id']);\n";
                $config_content .= "}\n\n";
                $config_content .= "function require_login() {\n";
                $config_content .= "    if (!is_logged_in()) {\n";
                $config_content .= "        redirect('login.php');\n";
                $config_content .= "    }\n";
                $config_content .= "}\n";
                
                file_put_contents('config.php', $config_content);
                
                // إنشاء المجلدات المطلوبة
                $directories = ['uploads', 'backups', 'uploads/products', 'uploads/barcodes'];
                foreach ($directories as $dir) {
                    if (!is_dir($dir)) {
                        mkdir($dir, 0755, true);
                    }
                }
                
                unset($_SESSION['db_config']);
                $step = 4;
                $success = 'تم تثبيت النظام بنجاح';
                
            } catch (Exception $e) {
                $error = $e->getMessage();
            }
            break;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام إدارة المحل Z</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        .install-container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .install-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .logo {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
        }
        .step.active {
            background: #667eea;
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <div class="logo">Z</div>
            <h2>نظام إدارة المحل</h2>
            <p>مرحباً بك في معالج التثبيت</p>
        </div>
        
        <div class="step-indicator">
            <div class="step <?= $step >= 1 ? ($step > 1 ? 'completed' : 'active') : '' ?>">1</div>
            <div class="step <?= $step >= 2 ? ($step > 2 ? 'completed' : 'active') : '' ?>">2</div>
            <div class="step <?= $step >= 3 ? ($step > 3 ? 'completed' : 'active') : '' ?>">3</div>
            <div class="step <?= $step >= 4 ? 'active' : '' ?>">4</div>
        </div>
        
        <div class="p-4">
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> <?= $error ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?= $success ?>
                </div>
            <?php endif; ?>
            
            <?php if ($step == 1): ?>
                <h4>الخطوة 1: التحقق من المتطلبات</h4>
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center p-2 border-bottom">
                        <span>إصدار PHP (7.4+)</span>
                        <span class="badge <?= version_compare(PHP_VERSION, '7.4.0', '>=') ? 'bg-success' : 'bg-danger' ?>">
                            <?= PHP_VERSION ?>
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center p-2 border-bottom">
                        <span>MySQLi Extension</span>
                        <span class="badge <?= extension_loaded('mysqli') ? 'bg-success' : 'bg-danger' ?>">
                            <?= extension_loaded('mysqli') ? 'متوفر' : 'غير متوفر' ?>
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center p-2 border-bottom">
                        <span>GD Extension</span>
                        <span class="badge <?= extension_loaded('gd') ? 'bg-success' : 'bg-danger' ?>">
                            <?= extension_loaded('gd') ? 'متوفر' : 'غير متوفر' ?>
                        </span>
                    </div>
                </div>
                <form method="post">
                    <button type="submit" class="btn btn-primary w-100">التالي</button>
                </form>
                
            <?php elseif ($step == 2): ?>
                <h4>الخطوة 2: إعداد قاعدة البيانات</h4>
                <form method="post">
                    <div class="mb-3">
                        <label class="form-label">خادم قاعدة البيانات</label>
                        <input type="text" class="form-control" name="db_host" value="localhost" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">اسم قاعدة البيانات</label>
                        <input type="text" class="form-control" name="db_name" value="z_store" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" name="db_user" value="root" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" name="db_pass">
                    </div>
                    <button type="submit" class="btn btn-primary w-100">إنشاء قاعدة البيانات</button>
                </form>
                
            <?php elseif ($step == 3): ?>
                <h4>الخطوة 3: إعداد المتجر</h4>
                <form method="post">
                    <div class="mb-3">
                        <label class="form-label">اسم المتجر</label>
                        <input type="text" class="form-control" name="store_name" value="محل Z" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">عنوان المتجر</label>
                        <textarea class="form-control" name="store_address" rows="2"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">رقم الهاتف</label>
                        <input type="text" class="form-control" name="store_phone">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" name="store_email">
                    </div>
                    <hr>
                    <h5>بيانات المدير</h5>
                    <div class="mb-3">
                        <label class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" name="admin_username" value="admin" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" name="admin_password" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الاسم الكامل</label>
                        <input type="text" class="form-control" name="admin_name" value="المدير العام" required>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">إنهاء التثبيت</button>
                </form>
                
            <?php elseif ($step == 4): ?>
                <div class="text-center">
                    <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                    <h4 class="mt-3">تم التثبيت بنجاح!</h4>
                    <p>يمكنك الآن استخدام نظام إدارة المحل Z</p>
                    <a href="index.php" class="btn btn-success btn-lg">دخول النظام</a>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
