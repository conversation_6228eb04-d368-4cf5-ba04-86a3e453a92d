<?php
/**
 * نظام إدارة المحل Z - النسخ الاحتياطي وضبط المصنع
 */

require_once 'config.php';
require_login();

// التحقق من صلاحيات المدير
if ($_SESSION['role'] !== 'admin') {
    die('غير مسموح لك بالوصول لهذه الصفحة');
}

$message = '';
$error = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'create_backup':
                    $backup_file = createDatabaseBackup();
                    $message = "تم إنشاء النسخة الاحتياطية بنجاح: $backup_file";
                    break;
                    
                case 'restore_backup':
                    if (isset($_FILES['backup_file']) && $_FILES['backup_file']['error'] === UPLOAD_ERR_OK) {
                        restoreDatabaseBackup($_FILES['backup_file']['tmp_name']);
                        $message = 'تم استعادة النسخة الاحتياطية بنجاح';
                    } else {
                        throw new Exception('يرجى اختيار ملف النسخة الاحتياطية');
                    }
                    break;
                    
                case 'factory_reset':
                    $confirm_text = $_POST['confirm_text'] ?? '';
                    if ($confirm_text !== 'RESET') {
                        throw new Exception('يرجى كتابة "RESET" للتأكيد');
                    }
                    
                    factoryReset();
                    $message = 'تم إعادة ضبط المصنع بنجاح';
                    break;
                    
                case 'delete_backup':
                    $backup_file = $_POST['backup_file'];
                    if (file_exists("backups/$backup_file")) {
                        unlink("backups/$backup_file");
                        $message = 'تم حذف النسخة الاحتياطية';
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// دالة إنشاء نسخة احتياطية
function createDatabaseBackup() {
    global $pdo;
    
    $backup_dir = 'backups';
    if (!is_dir($backup_dir)) {
        mkdir($backup_dir, 0755, true);
    }
    
    $filename = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
    $filepath = $backup_dir . '/' . $filename;
    
    $backup_content = "-- نسخة احتياطية لنظام إدارة المحل Z\n";
    $backup_content .= "-- تاريخ الإنشاء: " . date('Y-m-d H:i:s') . "\n\n";
    $backup_content .= "SET FOREIGN_KEY_CHECKS = 0;\n\n";
    
    // جلب قائمة الجداول
    $tables = [];
    $stmt = $pdo->query("SHOW TABLES");
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $tables[] = $row[0];
    }
    
    foreach ($tables as $table) {
        // هيكل الجدول
        $stmt = $pdo->query("SHOW CREATE TABLE `$table`");
        $create_table = $stmt->fetch(PDO::FETCH_ASSOC);
        $backup_content .= "DROP TABLE IF EXISTS `$table`;\n";
        $backup_content .= $create_table['Create Table'] . ";\n\n";
        
        // بيانات الجدول
        $stmt = $pdo->query("SELECT * FROM `$table`");
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($rows)) {
            $columns = array_keys($rows[0]);
            $backup_content .= "INSERT INTO `$table` (`" . implode('`, `', $columns) . "`) VALUES\n";
            
            $values = [];
            foreach ($rows as $row) {
                $escaped_values = [];
                foreach ($row as $value) {
                    if ($value === null) {
                        $escaped_values[] = 'NULL';
                    } else {
                        $escaped_values[] = "'" . addslashes($value) . "'";
                    }
                }
                $values[] = '(' . implode(', ', $escaped_values) . ')';
            }
            
            $backup_content .= implode(",\n", $values) . ";\n\n";
        }
    }
    
    $backup_content .= "SET FOREIGN_KEY_CHECKS = 1;\n";
    
    file_put_contents($filepath, $backup_content);
    
    return $filename;
}

// دالة استعادة النسخة الاحتياطية
function restoreDatabaseBackup($backup_file) {
    global $pdo;
    
    $sql_content = file_get_contents($backup_file);
    if ($sql_content === false) {
        throw new Exception('فشل في قراءة ملف النسخة الاحتياطية');
    }
    
    // تقسيم الاستعلامات
    $queries = explode(';', $sql_content);
    
    $pdo->beginTransaction();
    
    try {
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query) && !preg_match('/^(--|\/\*)/', $query)) {
                $pdo->exec($query);
            }
        }
        
        $pdo->commit();
    } catch (Exception $e) {
        $pdo->rollBack();
        throw new Exception('فشل في استعادة النسخة الاحتياطية: ' . $e->getMessage());
    }
}

// دالة إعادة ضبط المصنع
function factoryReset() {
    global $pdo;
    
    $pdo->beginTransaction();
    
    try {
        // حذف جميع البيانات مع الاحتفاظ بالهيكل
        $tables_to_clear = [
            'stock_movements',
            'sale_items',
            'sales',
            'purchase_items', 
            'purchases',
            'cash_register',
            'expenses'
        ];
        
        foreach ($tables_to_clear as $table) {
            $pdo->exec("DELETE FROM $table");
            $pdo->exec("ALTER TABLE $table AUTO_INCREMENT = 1");
        }
        
        // إعادة تعيين بيانات المنتجات
        $pdo->exec("UPDATE products SET stock_quantity = 0");
        
        // إعادة تعيين بيانات العملاء
        $pdo->exec("UPDATE customers SET wallet_balance = 0, debt_balance = 0, total_purchases = 0");
        
        // إعادة تعيين بيانات الموردين
        $pdo->exec("UPDATE suppliers SET balance = 0");
        
        // حذف المنتجات والعملاء والموردين غير الافتراضيين
        $pdo->exec("DELETE FROM products WHERE id > 0");
        $pdo->exec("DELETE FROM customers WHERE id > 1");
        $pdo->exec("DELETE FROM suppliers WHERE id > 0");
        $pdo->exec("DELETE FROM categories WHERE id > 1");
        
        // إعادة تعيين الإعدادات للقيم الافتراضية
        $default_settings = [
            'store_name' => 'محل Z',
            'store_address' => '',
            'store_phone' => '',
            'store_email' => '',
            'currency' => 'ج.م',
            'tax_rate' => '0',
            'receipt_footer' => 'شكراً لتعاملكم معنا',
            'low_stock_alert' => '1',
            'backup_frequency' => 'daily'
        ];
        
        foreach ($default_settings as $name => $value) {
            $stmt = $pdo->prepare("UPDATE settings SET setting_value = ? WHERE setting_name = ?");
            $stmt->execute([$value, $name]);
        }
        
        $pdo->commit();
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw new Exception('فشل في إعادة ضبط المصنع: ' . $e->getMessage());
    }
}

// جلب قائمة النسخ الاحتياطية
$backup_files = [];
$backup_dir = 'backups';
if (is_dir($backup_dir)) {
    $files = scandir($backup_dir);
    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) === 'sql') {
            $filepath = $backup_dir . '/' . $file;
            $backup_files[] = [
                'name' => $file,
                'size' => filesize($filepath),
                'date' => date('Y-m-d H:i:s', filemtime($filepath))
            ];
        }
    }
    
    // ترتيب حسب التاريخ (الأحدث أولاً)
    usort($backup_files, function($a, $b) {
        return strcmp($b['date'], $a['date']);
    });
}

// جلب إحصائيات قاعدة البيانات
try {
    $stats = [];
    
    $tables = ['products', 'customers', 'suppliers', 'sales', 'purchases'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
        $stats[$table] = $stmt->fetchColumn();
    }
    
    // حجم قاعدة البيانات
    $stmt = $pdo->query("
        SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS db_size_mb
        FROM information_schema.tables 
        WHERE table_schema = DATABASE()
    ");
    $stats['db_size'] = $stmt->fetchColumn();
    
} catch (PDOException $e) {
    $stats = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>النسخ الاحتياطي وضبط المصنع - نظام إدارة المحل Z</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">النسخ الاحتياطي وضبط المصنع</h1>
                </div>
                
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- إحصائيات قاعدة البيانات -->
                <?php if (!empty($stats)): ?>
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title"><?= number_format($stats['products']) ?></h5>
                                <p class="card-text">المنتجات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title"><?= number_format($stats['customers']) ?></h5>
                                <p class="card-text">العملاء</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title"><?= number_format($stats['suppliers']) ?></h5>
                                <p class="card-text">الموردين</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title"><?= number_format($stats['sales']) ?></h5>
                                <p class="card-text">المبيعات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title"><?= number_format($stats['purchases']) ?></h5>
                                <p class="card-text">المشتريات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title"><?= $stats['db_size'] ?> MB</h5>
                                <p class="card-text">حجم قاعدة البيانات</p>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="row">
                    <!-- إنشاء نسخة احتياطية -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-download"></i> إنشاء نسخة احتياطية
                                </h5>
                            </div>
                            <div class="card-body">
                                <p>إنشاء نسخة احتياطية كاملة من قاعدة البيانات تتضمن جميع البيانات والإعدادات.</p>
                                
                                <form method="post">
                                    <input type="hidden" name="action" value="create_backup">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-download"></i> إنشاء نسخة احتياطية الآن
                                    </button>
                                </form>
                                
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        سيتم حفظ النسخة الاحتياطية في مجلد backups
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- استعادة نسخة احتياطية -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-upload"></i> استعادة نسخة احتياطية
                                </h5>
                            </div>
                            <div class="card-body">
                                <p>استعادة البيانات من نسخة احتياطية سابقة. سيتم استبدال جميع البيانات الحالية.</p>
                                
                                <form method="post" enctype="multipart/form-data" onsubmit="return confirm('هل أنت متأكد؟ سيتم استبدال جميع البيانات الحالية!')">
                                    <input type="hidden" name="action" value="restore_backup">
                                    
                                    <div class="mb-3">
                                        <label class="form-label">اختر ملف النسخة الاحتياطية</label>
                                        <input type="file" class="form-control" name="backup_file" accept=".sql" required>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-info">
                                        <i class="fas fa-upload"></i> استعادة النسخة الاحتياطية
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- قائمة النسخ الاحتياطية -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i> النسخ الاحتياطية المحفوظة
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($backup_files)): ?>
                            <p class="text-muted text-center">لا توجد نسخ احتياطية محفوظة</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>اسم الملف</th>
                                            <th>التاريخ</th>
                                            <th>الحجم</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($backup_files as $backup): ?>
                                            <tr>
                                                <td><?= htmlspecialchars($backup['name']) ?></td>
                                                <td><?= $backup['date'] ?></td>
                                                <td><?= number_format($backup['size'] / 1024, 2) ?> KB</td>
                                                <td>
                                                    <a href="backups/<?= htmlspecialchars($backup['name']) ?>" 
                                                       class="btn btn-sm btn-primary" download>
                                                        <i class="fas fa-download"></i> تحميل
                                                    </a>
                                                    <form method="post" style="display: inline;" 
                                                          onsubmit="return confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')">
                                                        <input type="hidden" name="action" value="delete_backup">
                                                        <input type="hidden" name="backup_file" value="<?= htmlspecialchars($backup['name']) ?>">
                                                        <button type="submit" class="btn btn-sm btn-danger">
                                                            <i class="fas fa-trash"></i> حذف
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- إعادة ضبط المصنع -->
                <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle"></i> إعادة ضبط المصنع
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <strong>تحذير!</strong> هذا الإجراء سيحذف جميع البيانات ويعيد النظام للحالة الافتراضية.
                            لا يمكن التراجع عن هذا الإجراء!
                        </div>
                        
                        <p>سيتم حذف:</p>
                        <ul>
                            <li>جميع المبيعات والمشتريات</li>
                            <li>جميع المنتجات والعملاء والموردين</li>
                            <li>جميع حركات المخزون والخزنة</li>
                            <li>إعادة تعيين الإعدادات للقيم الافتراضية</li>
                        </ul>
                        
                        <form method="post" onsubmit="return confirmFactoryReset()">
                            <input type="hidden" name="action" value="factory_reset">
                            
                            <div class="mb-3">
                                <label class="form-label">اكتب "RESET" للتأكيد:</label>
                                <input type="text" class="form-control" name="confirm_text" required>
                            </div>
                            
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-exclamation-triangle"></i> إعادة ضبط المصنع
                            </button>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmFactoryReset() {
            const confirmText = document.querySelector('input[name="confirm_text"]').value;
            
            if (confirmText !== 'RESET') {
                alert('يجب كتابة "RESET" بالضبط للتأكيد');
                return false;
            }
            
            return confirm('هل أنت متأكد تماماً من إعادة ضبط المصنع؟ سيتم حذف جميع البيانات نهائياً!');
        }
    </script>
</body>
</html>
