<?php
/**
 * نظام إدارة المحل Z - جلب المنتجات للـ POS
 */

require_once '../config.php';
require_login();

header('Content-Type: application/json');

try {
    $stmt = $pdo->prepare("
        SELECT p.*, c.name as category_name 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.is_active = 1 AND p.stock_quantity > 0 
        ORDER BY p.name
    ");
    $stmt->execute();
    $products = $stmt->fetchAll();
    
    echo json_encode($products);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في جلب المنتجات']);
}
?>
