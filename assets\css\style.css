/* نظام إدارة المحل Z - ملف الأنماط الرئيسي */

/* الخطوط والألوان الأساسية */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

/* إعدادات عامة */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    direction: rtl;
    text-align: right;
}

/* البطاقات */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    border: none;
}

/* الأزرار */
.btn {
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
    transform: translateY(-1px);
}

.btn-lg {
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
}

/* النماذج */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* الجداول */
.table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

.table td {
    vertical-align: middle;
    text-align: center;
}

/* البطاقات الإحصائية */
.border-right-primary {
    border-right: 4px solid var(--primary-color) !important;
}

.border-right-success {
    border-right: 4px solid var(--success-color) !important;
}

.border-right-info {
    border-right: 4px solid var(--info-color) !important;
}

.border-right-warning {
    border-right: 4px solid var(--warning-color) !important;
}

/* التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
}

.alert-warning {
    background: linear-gradient(45deg, #fff3cd, #ffeaa7);
    color: #856404;
}

.alert-info {
    background: linear-gradient(45deg, #d1ecf1, #74b9ff);
    color: #0c5460;
}

.alert-success {
    background: linear-gradient(45deg, #d4edda, #00b894);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(45deg, #f8d7da, #e17055);
    color: #721c24;
}

/* الشارات */
.badge {
    border-radius: 20px;
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
}

/* نقطة البيع */
.pos-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.product-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
}

.product-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.product-card.selected {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
}

.product-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
}

.product-name {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.product-price {
    color: var(--primary-color);
    font-weight: bold;
    font-size: 1.1rem;
}

.product-stock {
    font-size: 0.8rem;
    color: #6c757d;
}

/* سلة التسوق */
.cart-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    height: fit-content;
    position: sticky;
    top: 100px;
}

.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item-name {
    font-weight: 600;
    color: var(--dark-color);
}

.cart-item-price {
    color: var(--primary-color);
    font-weight: bold;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border: none;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.quantity-btn:hover {
    background: var(--secondary-color);
}

.quantity-input {
    width: 60px;
    text-align: center;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 0.25rem;
}

/* الباركود */
.barcode-container {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.barcode-preview {
    border: 2px dashed #e9ecef;
    border-radius: var(--border-radius);
    padding: 2rem;
    margin: 1rem 0;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* البحث المتقدم */
.search-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.search-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

/* التقارير */
.report-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.chart-container {
    position: relative;
    height: 400px;
    margin: 1rem 0;
}

/* الطباعة */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 0.5rem;
    }
    
    .cart-container {
        position: relative;
        top: auto;
        margin-top: 1rem;
    }
    
    .search-filters {
        grid-template-columns: 1fr;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* تحسينات إضافية */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* تحسين النصوص */
.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
}

/* أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.action-buttons .btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* تحسين القوائم المنسدلة */
.dropdown-menu {
    border: none;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border-radius: var(--border-radius);
}

.dropdown-item {
    transition: var(--transition);
}

.dropdown-item:hover {
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    transform: translateX(5px);
}

/* تحسين المودال */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

/* تحسين التبويبات */
.nav-tabs .nav-link {
    border: none;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    transition: var(--transition);
}

.nav-tabs .nav-link.active {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
}

/* تحسين شريط التقدم */
.progress {
    border-radius: 20px;
    height: 8px;
}

.progress-bar {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: 20px;
}
