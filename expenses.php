<?php
/**
 * نظام إدارة المحل Z - إدارة المصروفات
 */

require_once 'config.php';
require_login();

$action = $_GET['action'] ?? 'list';
$expense_id = $_GET['id'] ?? null;
$message = '';
$error = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if ($action === 'add' || $action === 'edit') {
            $category = trim($_POST['category']);
            $amount = floatval($_POST['amount']);
            $description = trim($_POST['description']);
            $expense_date = $_POST['expense_date'];
            
            // التحقق من البيانات المطلوبة
            if (empty($category)) {
                throw new Exception('يرجى إدخال فئة المصروف');
            }
            
            if ($amount <= 0) {
                throw new Exception('يرجى إدخال مبلغ صحيح');
            }
            
            if ($action === 'add') {
                // إضافة مصروف جديد
                $stmt = $pdo->prepare("
                    INSERT INTO expenses (category, amount, description, expense_date, created_by) 
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([$category, $amount, $description, $expense_date, $_SESSION['user_id']]);
                
                // تحديث الخزنة
                $stmt = $pdo->prepare("SELECT balance_after FROM cash_register ORDER BY id DESC LIMIT 1");
                $stmt->execute();
                $current_balance = $stmt->fetchColumn() ?: 0;
                
                $stmt = $pdo->prepare("
                    INSERT INTO cash_register (transaction_type, amount, balance_before, balance_after, 
                                             reference_type, description, created_by) 
                    VALUES ('expense', ?, ?, ?, 'manual', ?, ?)
                ");
                $stmt->execute([
                    $amount, 
                    $current_balance, 
                    $current_balance - $amount,
                    "مصروف - $category: $description",
                    $_SESSION['user_id']
                ]);
                
                $message = 'تم إضافة المصروف بنجاح';
                
            } else {
                // تعديل مصروف موجود
                $stmt = $pdo->prepare("
                    UPDATE expenses 
                    SET category = ?, amount = ?, description = ?, expense_date = ? 
                    WHERE id = ?
                ");
                $stmt->execute([$category, $amount, $description, $expense_date, $expense_id]);
                
                $message = 'تم تحديث المصروف بنجاح';
            }
            
        } elseif ($action === 'delete') {
            $expense_id = $_POST['expense_id'];
            
            // حذف المصروف
            $stmt = $pdo->prepare("DELETE FROM expenses WHERE id = ?");
            $stmt->execute([$expense_id]);
            
            $message = 'تم حذف المصروف';
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// جلب البيانات للعرض
if ($action === 'edit' && $expense_id) {
    $stmt = $pdo->prepare("SELECT * FROM expenses WHERE id = ?");
    $stmt->execute([$expense_id]);
    $expense = $stmt->fetch();
    
    if (!$expense) {
        $error = 'المصروف غير موجود';
        $action = 'list';
    }
}

// جلب المصروفات للعرض
if ($action === 'list') {
    $search = $_GET['search'] ?? '';
    $category_filter = $_GET['category'] ?? '';
    $date_from = $_GET['date_from'] ?? '';
    $date_to = $_GET['date_to'] ?? '';
    
    $sql = "SELECT * FROM expenses WHERE 1=1";
    $params = [];
    
    if ($search) {
        $sql .= " AND (description LIKE ? OR category LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if ($category_filter) {
        $sql .= " AND category = ?";
        $params[] = $category_filter;
    }
    
    if ($date_from) {
        $sql .= " AND expense_date >= ?";
        $params[] = $date_from;
    }
    
    if ($date_to) {
        $sql .= " AND expense_date <= ?";
        $params[] = $date_to;
    }
    
    $sql .= " ORDER BY expense_date DESC, created_at DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $expenses = $stmt->fetchAll();
}

// جلب فئات المصروفات
$stmt = $pdo->prepare("SELECT DISTINCT category FROM expenses ORDER BY category");
$stmt->execute();
$categories = $stmt->fetchAll(PDO::FETCH_COLUMN);

// جلب إحصائيات المصروفات
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM expenses");
    $stmt->execute();
    $total_expenses_count = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COALESCE(SUM(amount), 0) FROM expenses WHERE MONTH(expense_date) = MONTH(CURDATE()) AND YEAR(expense_date) = YEAR(CURDATE())");
    $stmt->execute();
    $monthly_expenses = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COALESCE(SUM(amount), 0) FROM expenses WHERE DATE(expense_date) = CURDATE()");
    $stmt->execute();
    $daily_expenses = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COALESCE(SUM(amount), 0) FROM expenses");
    $stmt->execute();
    $total_expenses_amount = $stmt->fetchColumn();
    
} catch (PDOException $e) {
    $total_expenses_count = $monthly_expenses = $daily_expenses = $total_expenses_amount = 0;
}

// فئات المصروفات الافتراضية
$default_categories = [
    'إيجار',
    'كهرباء',
    'مياه',
    'هاتف وإنترنت',
    'رواتب',
    'صيانة',
    'وقود',
    'مواصلات',
    'قرطاسية',
    'تسويق',
    'أخرى'
];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المصروفات - نظام إدارة المحل Z</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <?php if ($action === 'add'): ?>
                            إضافة مصروف جديد
                        <?php elseif ($action === 'edit'): ?>
                            تعديل المصروف
                        <?php else: ?>
                            إدارة المصروفات
                        <?php endif; ?>
                    </h1>
                    
                    <?php if ($action === 'list'): ?>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="expenses.php?action=add" class="btn btn-sm btn-primary">
                                <i class="fas fa-plus"></i> إضافة مصروف
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
                
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($action === 'list'): ?>
                    <!-- إحصائيات المصروفات -->
                    <div class="row mb-4">
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                إجمالي المصروفات
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($total_expenses_count) ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-receipt fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                مصروفات اليوم
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($daily_expenses, 2) ?> ج.م
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-info shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                مصروفات الشهر
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($monthly_expenses, 2) ?> ج.م
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-right-danger shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col me-2">
                                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                                إجمالي المبلغ
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= number_format($total_expenses_amount, 2) ?> ج.م
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if ($action === 'add' || $action === 'edit'): ?>
                    <!-- نموذج إضافة/تعديل المصروف -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <?= $action === 'add' ? 'إضافة مصروف جديد' : 'تعديل المصروف' ?>
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="post" class="needs-validation" novalidate>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">فئة المصروف *</label>
                                            <input type="text" class="form-control" name="category" 
                                                   value="<?= htmlspecialchars($expense['category'] ?? '') ?>" 
                                                   list="categories" required>
                                            <datalist id="categories">
                                                <?php foreach ($default_categories as $cat): ?>
                                                    <option value="<?= htmlspecialchars($cat) ?>">
                                                <?php endforeach; ?>
                                                <?php foreach ($categories as $cat): ?>
                                                    <option value="<?= htmlspecialchars($cat) ?>">
                                                <?php endforeach; ?>
                                            </datalist>
                                            <div class="invalid-feedback">يرجى إدخال فئة المصروف</div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">المبلغ *</label>
                                            <input type="number" class="form-control" name="amount" 
                                                   step="0.01" min="0.01"
                                                   value="<?= htmlspecialchars($expense['amount'] ?? '') ?>" required>
                                            <div class="invalid-feedback">يرجى إدخال مبلغ صحيح</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">تاريخ المصروف *</label>
                                            <input type="date" class="form-control" name="expense_date" 
                                                   value="<?= htmlspecialchars($expense['expense_date'] ?? date('Y-m-d')) ?>" required>
                                            <div class="invalid-feedback">يرجى إدخال تاريخ المصروف</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" name="description" rows="3"><?= htmlspecialchars($expense['description'] ?? '') ?></textarea>
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i>
                                        <?= $action === 'add' ? 'إضافة المصروف' : 'حفظ التغييرات' ?>
                                    </button>
                                    <a href="expenses.php" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> إلغاء
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                <?php else: ?>
                    <!-- قائمة المصروفات -->
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h5 class="mb-0">قائمة المصروفات</h5>
                                </div>
                                <div class="col-md-6">
                                    <form method="get" class="d-flex gap-2">
                                        <input type="text" class="form-control form-control-sm" name="search" 
                                               placeholder="البحث..." value="<?= htmlspecialchars($_GET['search'] ?? '') ?>">
                                        <select class="form-select form-select-sm" name="category">
                                            <option value="">جميع الفئات</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?= htmlspecialchars($category) ?>" 
                                                        <?= ($_GET['category'] ?? '') === $category ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($category) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <input type="date" class="form-control form-control-sm" name="date_from" 
                                               value="<?= htmlspecialchars($_GET['date_from'] ?? '') ?>" placeholder="من تاريخ">
                                        <input type="date" class="form-control form-control-sm" name="date_to" 
                                               value="<?= htmlspecialchars($_GET['date_to'] ?? '') ?>" placeholder="إلى تاريخ">
                                        <button type="submit" class="btn btn-sm btn-primary">بحث</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>الفئة</th>
                                            <th>الوصف</th>
                                            <th>المبلغ</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (empty($expenses)): ?>
                                            <tr>
                                                <td colspan="5" class="text-center text-muted py-4">
                                                    لا توجد مصروفات
                                                </td>
                                            </tr>
                                        <?php else: ?>
                                            <?php 
                                            $total_filtered = 0;
                                            foreach ($expenses as $expense): 
                                                $total_filtered += $expense['amount'];
                                            ?>
                                                <tr>
                                                    <td><?= date('Y-m-d', strtotime($expense['expense_date'])) ?></td>
                                                    <td>
                                                        <span class="badge bg-secondary"><?= htmlspecialchars($expense['category']) ?></span>
                                                    </td>
                                                    <td><?= htmlspecialchars($expense['description']) ?></td>
                                                    <td>
                                                        <strong class="text-danger"><?= number_format($expense['amount'], 2) ?> ج.م</strong>
                                                    </td>
                                                    <td>
                                                        <div class="action-buttons">
                                                            <a href="expenses.php?action=edit&id=<?= $expense['id'] ?>" 
                                                               class="btn btn-sm btn-primary" title="تعديل">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <button type="button" class="btn btn-sm btn-danger" 
                                                                    onclick="deleteExpense(<?= $expense['id'] ?>, '<?= htmlspecialchars($expense['category']) ?>')" 
                                                                    title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                            
                                            <!-- إجمالي المصروفات المفلترة -->
                                            <tr class="table-info">
                                                <th colspan="3">إجمالي المصروفات المعروضة:</th>
                                                <th class="text-danger"><?= number_format($total_filtered, 2) ?> ج.م</th>
                                                <th></th>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function deleteExpense(expenseId, category) {
            if (confirm(`هل أنت متأكد من حذف مصروف "${category}"؟`)) {
                const form = document.createElement('form');
                form.method = 'post';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="expense_id" value="${expenseId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        // تفعيل التحقق من صحة النماذج
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
</body>
</html>
