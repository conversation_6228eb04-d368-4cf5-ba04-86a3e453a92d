<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- خلفية دائرية -->
  <circle cx="50" cy="50" r="45" fill="url(#logoGradient)" filter="url(#shadow)"/>
  
  <!-- حرف Z -->
  <g transform="translate(50,50)">
    <!-- الخط العلوي -->
    <rect x="-20" y="-15" width="40" height="6" fill="white" rx="3"/>
    
    <!-- الخط المائل -->
    <rect x="-2" y="-12" width="4" height="24" fill="white" rx="2" transform="rotate(25)"/>
    
    <!-- الخط السفلي -->
    <rect x="-20" y="9" width="40" height="6" fill="white" rx="3"/>
    
    <!-- نقاط زخرفية -->
    <circle cx="22" cy="-12" r="2" fill="white" opacity="0.8"/>
    <circle cx="-22" cy="12" r="2" fill="white" opacity="0.8"/>
  </g>
  
  <!-- نص صغير -->
  <text x="50" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white" font-weight="bold">STORE</text>
</svg>
