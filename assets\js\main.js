/**
 * نظام إدارة المحل Z - ملف JavaScript الرئيسي
 */

// متغيرات عامة
let cart = [];
let products = [];
let currentCustomer = null;

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeSystem();
});

// تهيئة النظام
function initializeSystem() {
    // تهيئة التولتيبس
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تهيئة البوبوفر
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // تهيئة البحث السريع
    initializeQuickSearch();
    
    // تهيئة نقطة البيع إذا كانت موجودة
    if (document.getElementById('pos-container')) {
        initializePOS();
    }
    
    // تهيئة الباركود إذا كان موجوداً
    if (document.getElementById('barcode-container')) {
        initializeBarcode();
    }
    
    // تهيئة النماذج
    initializeForms();
}

// تهيئة البحث السريع
function initializeQuickSearch() {
    const searchInput = document.getElementById('quickSearch');
    if (searchInput) {
        let searchTimeout;
        
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();
            
            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    performQuickSearch(query);
                }, 300);
            } else {
                hideSearchResults();
            }
        });
        
        // إخفاء النتائج عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.search-container')) {
                hideSearchResults();
            }
        });
    }
}

// تنفيذ البحث السريع
function performQuickSearch(query) {
    fetch(`ajax/quick_search.php?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            displaySearchResults(data);
        })
        .catch(error => {
            console.error('خطأ في البحث:', error);
        });
}

// عرض نتائج البحث
function displaySearchResults(results) {
    let resultsContainer = document.getElementById('search-results');
    
    if (!resultsContainer) {
        resultsContainer = document.createElement('div');
        resultsContainer.id = 'search-results';
        resultsContainer.className = 'search-results-dropdown';
        document.getElementById('quickSearch').parentNode.appendChild(resultsContainer);
    }
    
    if (results.length === 0) {
        resultsContainer.innerHTML = '<div class="search-result-item">لا توجد نتائج</div>';
    } else {
        resultsContainer.innerHTML = results.map(item => `
            <div class="search-result-item" onclick="selectSearchResult('${item.type}', ${item.id})">
                <i class="fas fa-${getIconForType(item.type)}"></i>
                <span>${item.name}</span>
                <small class="text-muted">${getTypeLabel(item.type)}</small>
            </div>
        `).join('');
    }
    
    resultsContainer.style.display = 'block';
}

// إخفاء نتائج البحث
function hideSearchResults() {
    const resultsContainer = document.getElementById('search-results');
    if (resultsContainer) {
        resultsContainer.style.display = 'none';
    }
}

// اختيار نتيجة البحث
function selectSearchResult(type, id) {
    const urls = {
        'product': `products.php?id=${id}`,
        'customer': `customers.php?id=${id}`,
        'supplier': `suppliers.php?id=${id}`,
        'sale': `sales.php?id=${id}`,
        'purchase': `purchases.php?id=${id}`
    };
    
    if (urls[type]) {
        window.location.href = urls[type];
    }
    
    hideSearchResults();
}

// الحصول على أيقونة النوع
function getIconForType(type) {
    const icons = {
        'product': 'box',
        'customer': 'user',
        'supplier': 'truck',
        'sale': 'shopping-bag',
        'purchase': 'shopping-cart'
    };
    return icons[type] || 'circle';
}

// الحصول على تسمية النوع
function getTypeLabel(type) {
    const labels = {
        'product': 'منتج',
        'customer': 'عميل',
        'supplier': 'مورد',
        'sale': 'فاتورة بيع',
        'purchase': 'فاتورة شراء'
    };
    return labels[type] || type;
}

// تهيئة نقطة البيع
function initializePOS() {
    loadProducts();
    updateCartDisplay();
    
    // تهيئة البحث عن المنتجات
    const productSearch = document.getElementById('product-search');
    if (productSearch) {
        productSearch.addEventListener('input', function() {
            filterProducts(this.value);
        });
    }
    
    // تهيئة قارئ الباركود
    initializeBarcodeScanner();
}

// تحميل المنتجات
function loadProducts() {
    fetch('ajax/get_products.php')
        .then(response => response.json())
        .then(data => {
            products = data;
            displayProducts(products);
        })
        .catch(error => {
            console.error('خطأ في تحميل المنتجات:', error);
            showAlert('خطأ في تحميل المنتجات', 'danger');
        });
}

// عرض المنتجات
function displayProducts(productsToShow) {
    const container = document.getElementById('products-grid');
    if (!container) return;
    
    container.innerHTML = productsToShow.map(product => `
        <div class="product-card" onclick="addToCart(${product.id})">
            <img src="${product.image_path || 'assets/images/no-image.png'}" 
                 alt="${product.name}" class="product-image">
            <div class="product-name">${product.name}</div>
            <div class="product-price">${formatCurrency(product.selling_price)}</div>
            <div class="product-stock">المخزون: ${product.stock_quantity}</div>
        </div>
    `).join('');
}

// فلترة المنتجات
function filterProducts(query) {
    const filtered = products.filter(product => 
        product.name.toLowerCase().includes(query.toLowerCase()) ||
        product.barcode.includes(query)
    );
    displayProducts(filtered);
}

// إضافة منتج للسلة
function addToCart(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;
    
    if (product.stock_quantity <= 0) {
        showAlert('هذا المنتج غير متوفر في المخزون', 'warning');
        return;
    }
    
    const existingItem = cart.find(item => item.id === productId);
    
    if (existingItem) {
        if (existingItem.quantity < product.stock_quantity) {
            existingItem.quantity++;
        } else {
            showAlert('لا يمكن إضافة كمية أكثر من المتوفر في المخزون', 'warning');
            return;
        }
    } else {
        cart.push({
            id: product.id,
            name: product.name,
            price: parseFloat(product.selling_price),
            quantity: 1,
            stock: product.stock_quantity
        });
    }
    
    updateCartDisplay();
    playSound('add-to-cart');
}

// حذف منتج من السلة
function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    updateCartDisplay();
}

// تحديث كمية المنتج في السلة
function updateCartQuantity(productId, newQuantity) {
    const item = cart.find(item => item.id === productId);
    if (!item) return;
    
    if (newQuantity <= 0) {
        removeFromCart(productId);
        return;
    }
    
    if (newQuantity > item.stock) {
        showAlert('الكمية المطلوبة أكبر من المتوفر في المخزون', 'warning');
        return;
    }
    
    item.quantity = newQuantity;
    updateCartDisplay();
}

// تحديث عرض السلة
function updateCartDisplay() {
    const cartContainer = document.getElementById('cart-items');
    const totalElement = document.getElementById('cart-total');
    
    if (!cartContainer || !totalElement) return;
    
    if (cart.length === 0) {
        cartContainer.innerHTML = '<div class="text-center text-muted">السلة فارغة</div>';
        totalElement.textContent = formatCurrency(0);
        return;
    }
    
    cartContainer.innerHTML = cart.map(item => `
        <div class="cart-item">
            <div>
                <div class="cart-item-name">${item.name}</div>
                <div class="cart-item-price">${formatCurrency(item.price)}</div>
            </div>
            <div class="quantity-controls">
                <button class="quantity-btn" onclick="updateCartQuantity(${item.id}, ${item.quantity - 1})">
                    <i class="fas fa-minus"></i>
                </button>
                <input type="number" class="quantity-input" value="${item.quantity}" 
                       onchange="updateCartQuantity(${item.id}, parseInt(this.value))" min="1" max="${item.stock}">
                <button class="quantity-btn" onclick="updateCartQuantity(${item.id}, ${item.quantity + 1})">
                    <i class="fas fa-plus"></i>
                </button>
                <button class="btn btn-sm btn-danger ms-2" onclick="removeFromCart(${item.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `).join('');
    
    const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    totalElement.textContent = formatCurrency(total);
}

// تهيئة قارئ الباركود
function initializeBarcodeScanner() {
    let barcodeBuffer = '';
    let barcodeTimeout;
    
    document.addEventListener('keypress', function(e) {
        // التحقق من أن المستخدم لا يكتب في حقل إدخال
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            return;
        }
        
        clearTimeout(barcodeTimeout);
        
        if (e.key === 'Enter') {
            if (barcodeBuffer.length > 0) {
                searchProductByBarcode(barcodeBuffer);
                barcodeBuffer = '';
            }
        } else {
            barcodeBuffer += e.key;
            
            // مسح البافر بعد ثانية واحدة
            barcodeTimeout = setTimeout(() => {
                barcodeBuffer = '';
            }, 1000);
        }
    });
}

// البحث عن منتج بالباركود
function searchProductByBarcode(barcode) {
    const product = products.find(p => p.barcode === barcode);
    
    if (product) {
        addToCart(product.id);
        showAlert(`تم إضافة ${product.name} للسلة`, 'success');
    } else {
        showAlert('لم يتم العثور على منتج بهذا الباركود', 'warning');
    }
}

// تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-EG', {
        style: 'currency',
        currency: 'EGP',
        minimumFractionDigits: 2
    }).format(amount).replace('EGP', 'ج.م');
}

// عرض تنبيه
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alert-container') || createAlertContainer();
    
    const alertElement = document.createElement('div');
    alertElement.className = `alert alert-${type} alert-dismissible fade show`;
    alertElement.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alertElement);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertElement.parentNode) {
            alertElement.remove();
        }
    }, 5000);
}

// إنشاء حاوية التنبيهات
function createAlertContainer() {
    const container = document.createElement('div');
    container.id = 'alert-container';
    container.style.position = 'fixed';
    container.style.top = '80px';
    container.style.right = '20px';
    container.style.zIndex = '9999';
    container.style.maxWidth = '400px';
    document.body.appendChild(container);
    return container;
}

// تشغيل صوت
function playSound(soundName) {
    // يمكن إضافة ملفات صوتية لاحقاً
    console.log(`Playing sound: ${soundName}`);
}

// تهيئة النماذج
function initializeForms() {
    // تهيئة التحقق من صحة النماذج
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        });
    });
    
    // تهيئة حقول الأرقام
    const numberInputs = document.querySelectorAll('input[type="number"]');
    numberInputs.forEach(input => {
        input.addEventListener('input', function() {
            if (this.value < 0) {
                this.value = 0;
            }
        });
    });
}

// تهيئة الباركود
function initializeBarcode() {
    const generateBtn = document.getElementById('generate-barcode');
    if (generateBtn) {
        generateBtn.addEventListener('click', generateBarcode);
    }
}

// إنتاج باركود
function generateBarcode() {
    const productSelect = document.getElementById('product-select');
    const barcodePreview = document.getElementById('barcode-preview');
    
    if (!productSelect || !barcodePreview) return;
    
    const productId = productSelect.value;
    if (!productId) {
        showAlert('يرجى اختيار منتج', 'warning');
        return;
    }
    
    fetch('ajax/generate_barcode.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ product_id: productId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            barcodePreview.innerHTML = `<img src="${data.barcode_url}" alt="Barcode">`;
            document.getElementById('print-barcode').style.display = 'block';
        } else {
            showAlert(data.message || 'خطأ في إنتاج الباركود', 'danger');
        }
    })
    .catch(error => {
        console.error('خطأ في إنتاج الباركود:', error);
        showAlert('خطأ في إنتاج الباركود', 'danger');
    });
}

// طباعة الباركود
function printBarcode() {
    const barcodePreview = document.getElementById('barcode-preview');
    if (!barcodePreview) return;
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>طباعة باركود</title>
                <style>
                    body { text-align: center; margin: 20px; }
                    img { max-width: 100%; }
                </style>
            </head>
            <body>
                ${barcodePreview.innerHTML}
                <script>window.print(); window.close();</script>
            </body>
        </html>
    `);
    printWindow.document.close();
}

// دوال مساعدة إضافية
function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
    return confirm(message);
}

function loading(element, show = true) {
    if (show) {
        element.classList.add('loading');
        element.disabled = true;
    } else {
        element.classList.remove('loading');
        element.disabled = false;
    }
}

// تصدير الدوال للاستخدام العام
window.ZStore = {
    addToCart,
    removeFromCart,
    updateCartQuantity,
    showAlert,
    formatCurrency,
    confirmDelete,
    loading,
    generateBarcode,
    printBarcode
};
